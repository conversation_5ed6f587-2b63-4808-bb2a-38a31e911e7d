import polars as pl

from core_silver.observation_converter.deduplicators.latest_deduplicator import (
    LatestDeduplicator,
)


class MonthlyLatestDeduplicator(LatestDeduplicator):
    """
    Deduplicator that selects the latest upload for each month.
    """

    def _generate_time_periods(self, metadata_df: pl.DataFrame) -> pl.DataFrame:
        """Generate monthly time periods from date ranges."""

        def generate_years(row):
            start_date = row["date_from"]
            end_date = row["date_to"]

            years = []
            current = start_date
            while current <= end_date:
                years.append(current.year)
                # Move to next month
                if current.month == 12:
                    current = current.replace(year=current.year + 1, month=1, day=1)
                else:
                    current = current.replace(month=current.month + 1, day=1)
            return years

        def generate_months(row):
            start_date = row["date_from"]
            end_date = row["date_to"]

            months = []
            current = start_date
            while current <= end_date:
                months.append(current.month)
                # Move to next month
                if current.month == 12:
                    current = current.replace(year=current.year + 1, month=1, day=1)
                else:
                    current = current.replace(month=current.month + 1, day=1)
            return months

        metadata_df = metadata_df.with_columns([
            pl.struct(["date_from", "date_to"])
            .map_elements(generate_years, return_dtype=pl.List(pl.Int64))
            .alias("years"),
            pl.struct(["date_from", "date_to"])
            .map_elements(generate_months, return_dtype=pl.List(pl.Int64))
            .alias("months"),
        ])

        # Explode both lists simultaneously
        metadata_df = metadata_df.explode(["years", "months"])
        return metadata_df.rename({"years": "year", "months": "month"})

    def _get_join_keys(self) -> list[str]:
        """Return join keys for monthly deduplication."""
        return ["report_id", "year", "month"]

    def _get_time_period_columns(self) -> list[str]:
        """Return time period columns for monthly deduplication."""
        return ["year", "month"]
