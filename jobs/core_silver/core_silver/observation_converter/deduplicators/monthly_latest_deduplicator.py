import polars as pl

from core_silver.observation_converter.deduplicators.base_deduplicator import (
    BaseDeduplicator,
)
from core_silver.utils.string_format import (
    get_date_from_from_filename,
    get_date_to_from_filename,
    get_report_id_from_filename,
)
from data_sdk.domain.domain_types import ReportMetadata, StudioId
from data_sdk.reports.reader import ConvertedReportsReader


class MonthlyLatestDeduplicator(BaseDeduplicator):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)

    def deduplicate(
        self,
        metadata_list: list[ReportMetadata],
        converted_filename_list: list[str],
        converted_reader: ConvertedReportsReader,
    ) -> pl.DataFrame:
        if not metadata_list or not converted_filename_list:
            return pl.DataFrame()

        coverage = self._get_coverage(metadata_list, converted_filename_list)

        reports_data = self._load_files(
            metadata_list[0].studio_id, converted_filename_list, converted_reader
        )
        if reports_data.is_empty():
            return pl.DataFrame()

        # Join on report_id, year, and month instead of date
        deduplicated = reports_data.join(
            coverage, on=["report_id", "year", "month"], how="inner"
        )
        return deduplicated

    def _get_coverage(
        self, metadata_list: list[ReportMetadata], converted_filepath_list: list[str]
    ) -> pl.DataFrame:
        """
        Generates coverage based on metadata and file names, operating on monthly granularity
        """
        from data_sdk.domain.domain_types import ReportState

        # Filter out failed reports
        valid_metadata = [
            obj for obj in metadata_list if obj.state != ReportState.FAILED
        ]
        if not valid_metadata:
            return pl.DataFrame()

        metadata_df = pl.DataFrame([vars(obj) for obj in valid_metadata])
        metadata_df = metadata_df[["report_id", "upload_date", "date_from", "date_to"]]

        # Create a DataFrame from the list of file names
        file_df = pl.DataFrame({"file_name": converted_filepath_list})

        # Extract report_id, date_from_from_file_name, and date_to_from_file_name
        file_df = file_df.with_columns(
            pl.col("file_name")
            .map_elements(get_report_id_from_filename, return_dtype=pl.Int64)
            .alias("report_id"),
            pl.col("file_name")
            .map_elements(get_date_from_from_filename, return_dtype=pl.Date)
            .alias("date_from_from_file_name"),
            pl.col("file_name")
            .map_elements(get_date_to_from_filename, return_dtype=pl.Date)
            .alias("date_to_from_file_name"),
        )
        file_df = file_df.drop("file_name")

        metadata_df = metadata_df.join(file_df, on="report_id", how="inner")

        metadata_df = metadata_df.drop(["date_from", "date_to"])
        metadata_df = metadata_df.rename({
            "date_from_from_file_name": "date_from",
            "date_to_from_file_name": "date_to",
        })

        # Generate monthly ranges instead of daily ranges
        def generate_years(row):
            start_date = row["date_from"]
            end_date = row["date_to"]

            years = []
            current = start_date
            while current <= end_date:
                years.append(current.year)
                # Move to next month
                if current.month == 12:
                    current = current.replace(year=current.year + 1, month=1, day=1)
                else:
                    current = current.replace(month=current.month + 1, day=1)
            return years

        def generate_months(row):
            start_date = row["date_from"]
            end_date = row["date_to"]

            months = []
            current = start_date
            while current <= end_date:
                months.append(current.month)
                # Move to next month
                if current.month == 12:
                    current = current.replace(year=current.year + 1, month=1, day=1)
                else:
                    current = current.replace(month=current.month + 1, day=1)
            return months

        metadata_df = metadata_df.with_columns([
            pl.struct(["date_from", "date_to"])
            .map_elements(generate_years, return_dtype=pl.List(pl.Int64))
            .alias("years"),
            pl.struct(["date_from", "date_to"])
            .map_elements(generate_months, return_dtype=pl.List(pl.Int64))
            .alias("months"),
        ])

        # Explode both lists simultaneously
        metadata_df = metadata_df.explode(["years", "months"])
        metadata_df = metadata_df.rename({"years": "year", "months": "month"})

        # Sort by upload_date (latest first), then by year and month
        metadata_df = metadata_df.sort(
            by=["upload_date", "year", "month"], descending=[True, False, False]
        )
        metadata_df = metadata_df.drop(["date_from", "date_to", "upload_date"])

        # Keep only the latest report for each (year, month) combination
        metadata_df = metadata_df.unique(subset=["year", "month"], keep="first")
        return metadata_df

    def _load_files(
        self,
        studio_id: StudioId,
        filenames: list[str],
        converted_reader: ConvertedReportsReader,
    ) -> pl.DataFrame:
        all_files = [
            converted_reader.read(f"studio_id={studio_id}/{filename}")
            for filename in filenames
        ]
        # Polars saves Enum column in empty df as Categorical
        # and we can't concat dfs with different columns types
        all_non_empty_files = [file for file in all_files if not file.is_empty()]
        if len(all_non_empty_files) == 0:
            return pl.DataFrame()
        return pl.concat([file for file in all_files if not file.is_empty()])
