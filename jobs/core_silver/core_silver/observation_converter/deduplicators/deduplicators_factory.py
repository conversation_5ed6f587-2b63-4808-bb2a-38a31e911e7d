from core_silver.observation_converter.deduplicators.base_deduplicator import (
    BaseDeduplicator,
)
from core_silver.observation_converter.deduplicators.daily_latest_deduplicator import (
    DailyLatestDeduplicator,
)
from core_silver.observation_converter.deduplicators.last_file_deduplicator import (
    LastFileDeduplicator,
)
from core_silver.observation_converter.deduplicators.monthly_latest_deduplicator import (
    MonthlyLatestDeduplicator,
)
from core_silver.observation_converter.deduplicators.persistant_upload_deduplicator import (
    PersistantUploadDeduplicator,
)
from core_silver.observation_converter.deduplicators.ppr_sales_deduplicator import (
    PortalPlatformRegionSalesDeduplicator,
)
from data_sdk.domain import Portal
from data_sdk.domain.observations import ObservationType

source_to_deduplicator: dict[tuple[Portal, ObservationType], type[BaseDeduplicator]] = {
    # SALES
    (Portal.APPLE, ObservationType.SALES): DailyLatestDeduplicator,
    (Portal.EPIC, ObservationType.SALES): DailyLatestDeduplicator,
    (Portal.GOG, ObservationType.SALES): DailyLatestDeduplicator,
    (Portal.GOOGLE, ObservationType.SALES): DailyLatestDeduplicator,
    (Portal.HUMBLE, ObservationType.SALES): DailyLatestDeduplicator,
    (Portal.META, ObservationType.SALES): PortalPlatformRegionSalesDeduplicator,
    (Portal.MICROSOFT, ObservationType.SALES): DailyLatestDeduplicator,
    (Portal.NINTENDO, ObservationType.SALES): PortalPlatformRegionSalesDeduplicator,
    (Portal.NINTENDO, ObservationType.WISHLIST_ACTIONS): DailyLatestDeduplicator,
    (Portal.PLAYSTATION, ObservationType.SALES): PortalPlatformRegionSalesDeduplicator,
    (Portal.STEAM, ObservationType.SALES): DailyLatestDeduplicator,
    # WISHLIST_COHORTS
    (Portal.STEAM, ObservationType.WISHLIST_COHORTS): DailyLatestDeduplicator,
    # WISHLIST_ACTIONS
    (Portal.PLAYSTATION, ObservationType.WISHLIST_ACTIONS): DailyLatestDeduplicator,
    (Portal.PLAYSTATION, ObservationType.WISHLIST_COHORTS): DailyLatestDeduplicator,
    (Portal.STEAM, ObservationType.WISHLIST_ACTIONS): DailyLatestDeduplicator,
    # VISIBILITY
    (Portal.STEAM, ObservationType.VISIBILITY): DailyLatestDeduplicator,
    (Portal.PLAYSTATION, ObservationType.VISIBILITY): DailyLatestDeduplicator,
    # DISCOUNTS
    (Portal.NINTENDO, ObservationType.DISCOUNTS): LastFileDeduplicator,
    (Portal.STEAM, ObservationType.DISCOUNTS): LastFileDeduplicator,
    # CUMULATIVE_WISHLIST_SALES
    (
        Portal.NINTENDO,
        ObservationType.CUMULATIVE_WISHLIST_SALES,
    ): PersistantUploadDeduplicator,
    # WISHLIST_BALANCE
    (Portal.STEAM, ObservationType.WISHLIST_BALANCE): DailyLatestDeduplicator,
    # DAILY_ACTIVE_USERS
    (Portal.MICROSOFT, ObservationType.DAILY_ACTIVE_USERS): DailyLatestDeduplicator,
    (Portal.STEAM, ObservationType.DAILY_ACTIVE_USERS): DailyLatestDeduplicator,
    # MONTHLY_ACTIVE_USERS
    (Portal.MICROSOFT, ObservationType.MONTHLY_ACTIVE_USERS): MonthlyLatestDeduplicator,
}


def get_deduplicator(
    portal: Portal, observation_type: ObservationType
) -> type[BaseDeduplicator]:
    return source_to_deduplicator[(portal, observation_type)]
