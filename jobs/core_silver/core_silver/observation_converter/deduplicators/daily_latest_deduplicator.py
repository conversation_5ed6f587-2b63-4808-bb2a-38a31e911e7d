import polars as pl

from core_silver.observation_converter.deduplicators.latest_deduplicator import (
    LatestDeduplicator,
)


class DailyLatestDeduplicator(LatestDeduplicator):
    """
    Deduplicator that selects the latest upload for each day.
    """

    def _generate_time_periods(self, metadata_df: pl.DataFrame) -> pl.DataFrame:
        """Generate daily time periods from date ranges."""

        def generate_date_range(row):
            return pl.date_range(
                row["date_from"], row["date_to"], "1d", eager=True
            ).to_list()

        metadata_df = metadata_df.with_columns(
            pl.struct(["date_from", "date_to"])
            .map_elements(generate_date_range, return_dtype=pl.List(pl.Date))
            .alias("date")
        )

        return metadata_df.explode("date")

    def _get_join_keys(self) -> list[str]:
        """Return join keys for daily deduplication."""
        return ["report_id", "date"]

    def _get_time_period_columns(self) -> list[str]:
        """Return time period columns for daily deduplication."""
        return ["date"]
