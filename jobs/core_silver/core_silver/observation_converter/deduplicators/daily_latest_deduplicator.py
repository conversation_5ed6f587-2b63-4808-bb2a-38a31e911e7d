import polars as pl

from core_silver.observation_converter.deduplicators.base_deduplicator import (
    BaseDeduplicator,
)
from core_silver.utils.string_format import (
    get_date_from_from_filename,
    get_date_to_from_filename,
    get_report_id_from_filename,
)
from data_sdk.domain.domain_types import ReportMetadata, StudioId
from data_sdk.reports.reader import ConvertedReportsReader


class DailyLatestDeduplicator(BaseDeduplicator):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)

    def deduplicate(
        self,
        metadata_list: list[ReportMetadata],
        converted_filename_list: list[str],
        converted_reader: ConvertedReportsReader,
    ) -> pl.DataFrame:
        if not metadata_list or not converted_filename_list:
            return pl.DataFrame()
        coverage = self._get_coverage(metadata_list, converted_filename_list)

        reports_data = self._load_files(
            metadata_list[0].studio_id, converted_filename_list, converted_reader
        )
        if reports_data.is_empty():
            return pl.DataFrame()

        # Polars inner join doesn't preserve ordering, unlike pandas
        deduplicated = reports_data.join(
            coverage, on=["report_id", "date"], how="inner"
        )
        return deduplicated

    def _get_coverage(
        self, metadata_list: list[ReportMetadata], converted_filepath_list: list[str]
    ) -> pl.DataFrame:
        """
        generates coverage based on metadata and based on downloaded file names
        """
        metadata_df = pl.DataFrame([vars(obj) for obj in metadata_list])
        metadata_df = metadata_df[["report_id", "upload_date", "date_from", "date_to"]]
        #
        # Create a DataFrame from the list of file names
        file_df = pl.DataFrame({"file_name": converted_filepath_list})

        # Extract report_id, date_from_from_file_name, and date_to_from_file_name
        file_df = file_df.with_columns(
            pl.col("file_name")
            .apply(get_report_id_from_filename, return_dtype=pl.Int64)
            .alias("report_id"),
            pl.col("file_name")
            .apply(get_date_from_from_filename, return_dtype=pl.Date)
            .alias("date_from_from_file_name"),
            pl.col("file_name")
            .apply(get_date_to_from_filename, return_dtype=pl.Date)
            .alias("date_to_from_file_name"),
        )
        file_df = file_df.drop("file_name")

        metadata_df = metadata_df.join(file_df, on="report_id", how="inner")

        metadata_df = metadata_df.drop(["date_from", "date_to"])
        metadata_df = metadata_df.rename({
            "date_from_from_file_name": "date_from",
            "date_to_from_file_name": "date_to",
        })

        def generate_date_range(row):
            return pl.date_range(
                row["date_from"], row["date_to"], "1d", eager=True
            ).to_list()

        metadata_df = metadata_df.with_columns(
            pl.struct(["date_from", "date_to"])
            .map_elements(generate_date_range, return_dtype=pl.List(pl.Date))
            .alias("date")
        )

        metadata_df = metadata_df.explode("date")

        metadata_df = metadata_df.sort(by=["upload_date", "date"], descending=True)
        metadata_df = metadata_df.drop(["date_from", "date_to", "upload_date"])
        metadata_df = metadata_df.unique(subset="date", keep="first")
        return metadata_df

    def _load_files(
        self,
        studio_id: StudioId,
        filenames: list[str],
        converted_reader: ConvertedReportsReader,
    ) -> pl.DataFrame:
        all_files = [
            converted_reader.read(f"studio_id={studio_id}/{filename}")
            for filename in filenames
        ]
        # Polars saves Enum column in empty df as Categorical
        # and we can't concat dfs with different columns types
        all_non_empty_files = [file for file in all_files if not file.is_empty()]
        if len(all_non_empty_files) == 0:
            return pl.DataFrame()
        return pl.concat([file for file in all_files if not file.is_empty()])
