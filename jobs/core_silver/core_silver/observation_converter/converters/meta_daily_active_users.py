from core_silver.observation_converter.converters.daily_active_users_converter import (
    BaseDailyActiveUsersConverter,
)
from data_sdk.domain import DisplayPortal, Portal
from data_sdk.domain.stores import Store


class MetaDailyActiveUsersConverter(BaseDailyActiveUsersConverter):
    """Meta-specific daily active users converter."""

    PORTAL = Portal.META
    DISPLAY_PORTAL = DisplayPortal.META
    STORE = Store.META_QUEST  # Example - could be META_QUEST or META_RIFT
