from core_silver.observation_converter.converters.daily_active_users_converter import (
    BaseDailyActiveUsersConverter,
)
from data_sdk.domain import DisplayPortal, Portal
from data_sdk.domain.stores import Store


class MetaDailyActiveUsersConverter(BaseDailyActiveUsersConverter):
    """Meta-specific daily active users converter."""

    def _get_portal(self) -> Portal:
        """Return Meta portal."""
        return Portal.META

    def _get_display_portal(self) -> DisplayPortal:
        """Return Meta display portal."""
        return DisplayPortal.META

    def _get_store(self) -> Store:
        """Return Meta store."""
        return Store.META
