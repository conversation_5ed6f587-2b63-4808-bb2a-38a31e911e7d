import abc
from zipfile import BadZipFile

import pandas as pd
import pandera as pa
from pandera import Column

from core_silver.observation_converter.common_columns import (
    generate_portal_platform_region,
    generate_unique_sku_id,
)
from core_silver.observation_converter.converters.base_converter import (
    BaseConverter,
    extract_csvs_from_raw_file,
    get_manifest_data_from_raw_file,
)
from core_silver.observation_converter.converters.exceptions import (
    FileExtractionError,
    FileSchemaError,
)
from core_silver.observation_converter.pandas_utils import InvalidSchema, enforce_schema
from data_sdk.domain import DisplayPortal, Portal
from data_sdk.domain.countries import get_country_alpha_3
from data_sdk.domain.platform import DisplayPlatform
from data_sdk.domain.regions import Region
from data_sdk.domain.stores import Store


class BaseActiveUsersConverter(BaseConverter, abc.ABC):
    """
    Base class for all active users converters (daily and monthly).
    Contains common logic for parsing and extracting active users data.
    """

    # Common schema for active users data
    _base_schema_fields = {
        "account": Column(pa.String, coerce=True),
        "portal": Column(pa.String, coerce=True),
        "product": Column(pa.String, coerce=True),
        "product_id": Column(pa.String, coerce=True),
        "count": Column(pa.Int, coerce=True),
    }

    def parse(self, raw_file: bytes) -> pd.DataFrame:
        """Parse raw file and enforce schema validation."""
        df = self._extract_files(raw_file)
        df.sort_index(ascending=True, inplace=True)
        try:
            return enforce_schema(df, self._schema)
        except InvalidSchema as ex:
            raise FileSchemaError(str(ex))

    def _convert(self) -> pd.DataFrame:
        """Convert parsed data to standardized format."""
        manifest = self._raw_report.metadata
        parsed_df = self.parse(self._raw_report.raw_file)

        if parsed_df.empty:
            return pd.DataFrame()

        converted_df = pd.DataFrame()
        converted_df = self._build_converted_dataframe(parsed_df, manifest)

        # Add common columns
        converted_df["portal_platform_region"] = generate_portal_platform_region(
            converted_df["portal"], converted_df["platform"], converted_df["region"]
        )
        converted_df["unique_sku_id"] = generate_unique_sku_id(
            converted_df, self._get_portal()
        )
        return converted_df

    def _extract_files(
        self, raw_file: bytes, file_list: list[str] | None = None
    ) -> pd.DataFrame:
        """Extract CSV files from raw zip file."""
        try:
            self.manifest_from_file = get_manifest_data_from_raw_file(raw_file)
            raw_df = extract_csvs_from_raw_file(
                raw_file,
                df_transform=None,
                file_list_filter=self._get_file_filter(),
                sep=",",
                header=0,
                parse_dates=self._get_parse_dates(),
                quotechar='"',
                keep_default_na=False,
                na_values=[""],
                dtype=self._get_dtype_mapping(),
            )
        except (ValueError, BadZipFile) as ex:
            raise FileExtractionError(str(ex))
        return raw_df

    @abc.abstractmethod
    def _build_converted_dataframe(
        self, parsed_df: pd.DataFrame, manifest
    ) -> pd.DataFrame:
        """
        Build the converted dataframe with time-specific columns.

        Args:
            parsed_df: The parsed dataframe from raw file
            manifest: Report metadata

        Returns:
            DataFrame with converted data including time columns
        """
        pass

    @abc.abstractmethod
    def _get_file_filter(self) -> callable:
        """Return the file filter function for extracting relevant files."""
        pass

    @abc.abstractmethod
    def _get_parse_dates(self) -> bool:
        """Return whether to parse dates during CSV extraction."""
        pass

    @abc.abstractmethod
    def _get_dtype_mapping(self) -> dict:
        """Return the dtype mapping for CSV parsing."""
        pass

    @abc.abstractmethod
    def _get_portal(self) -> Portal:
        """Return the portal enum for this converter."""
        pass

    @abc.abstractmethod
    def _get_display_portal(self) -> DisplayPortal:
        """Return the display portal for this converter."""
        pass

    @abc.abstractmethod
    def _get_store(self) -> Store:
        """Return the store enum for this converter."""
        pass

    def _get_common_columns(self, parsed_df: pd.DataFrame, manifest) -> dict:
        """Get common columns that are the same across all active users converters."""
        return {
            "sku_id": parsed_df["product_id"],
            "human_name": parsed_df["product"],
            "store_id": parsed_df["product_id"],
            "platform": DisplayPlatform.PC,
            "region": Region.GLOBAL,
            "portal": self._get_display_portal(),
            "store": self._get_store().value,
            "abbreviated_name": self._get_store().abbreviate(),
            "country_code": get_country_alpha_3("unknown"),
            "report_id": manifest.report_id,
            "studio_id": manifest.studio_id,
        }
