from core_silver.observation_converter.converters.daily_active_users_converter import (
    BaseDailyActiveUsersConverter,
)
from data_sdk.domain import DisplayPortal, Portal
from data_sdk.domain.stores import Store


class SteamDailyActiveUsersConverter(BaseDailyActiveUsersConverter):
    """Steam-specific daily active users converter."""

    PORTAL = Portal.STEAM
    DISPLAY_PORTAL = DisplayPortal.STEAM
    STORE = Store.STEAM
