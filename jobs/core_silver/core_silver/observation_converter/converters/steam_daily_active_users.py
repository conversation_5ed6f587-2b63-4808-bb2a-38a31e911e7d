from core_silver.observation_converter.converters.daily_active_users_converter import (
    BaseDailyActiveUsersConverter,
)
from data_sdk.domain import DisplayPortal, Portal
from data_sdk.domain.stores import Store


class SteamDailyActiveUsersConverter(BaseDailyActiveUsersConverter):
    """Steam-specific daily active users converter."""

    def _get_portal(self) -> Portal:
        """Return Steam portal."""
        return Portal.STEAM

    def _get_display_portal(self) -> DisplayPortal:
        """Return Steam display portal."""
        return DisplayPortal.STEAM

    def _get_store(self) -> Store:
        """Return Steam store."""
        return Store.STEAM
