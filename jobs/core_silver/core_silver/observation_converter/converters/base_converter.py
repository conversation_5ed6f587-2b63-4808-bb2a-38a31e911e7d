import hashlib
import io
from abc import ABC, abstractmethod
from typing import Callable, Type
from zipfile import ZipFile

import pandas as pd
import polars as pl
from elasticapm import capture_span

from core_silver.observation_converter.converters.exceptions import (
    FileExtractionError,
    ManifestFileNotFound,
)
from core_silver.observation_converter.converters.sale_category import (
    SaleCategoryClassifier,
)
from core_silver.observation_converter.zip import (
    get_report_names,
    is_zip,
    read_manifest_from_zip,
)
from data_sdk.domain.domain_types import ReportMetadataWithRawFile
from data_sdk.reports.schema import (
    BaseConvertedReport,
    CumulativeWishlistSalesConvertedReport,
    DiscountsConvertedReport,
    MonthlyActiveUsersConvertedReport,
    SalesConvertedReport,
    VisibilityConvertedReport,
    WishlistActionsConvertedReport,
    WishlistBalanceConvertedReport,
    WishlistCohortsConvertedReport,
)


def extract_csvs_from_raw_file(
    raw_file: bytes,
    df_transform: Callable[[pd.DataFrame, str], pd.DataFrame] | None = None,
    file_list: list[str] | None = None,
    file_list_filter: Callable[[str], bool] | None = lambda filename: True,
    **kwargs,
) -> pd.DataFrame:
    """
    Extracts csvs if raw_file is Zip archive, or returns single raw file and parses it to the pandas Dataframe

    Args:
    raw_file: Report raw file
    df_transform: Function that is modifying each file before concat, f.e. adding file names to the data frame
    **kwargs: Arguments that are proxied to the pandas read_csv function

    Returns:
    Resultant dataframe
    """
    with io.BytesIO(raw_file) as stream:
        if is_zip(raw_file):
            zip_file = ZipFile(stream)
            report_names = get_report_names(zip_file, ".csv")
            if file_list:
                report_names = [name for name in report_names if name in file_list]

            if file_list_filter is not None:
                report_names = [name for name in report_names if file_list_filter(name)]

            if not report_names:
                return pd.DataFrame()
            merged_df = pd.concat([
                df_transform(pd.read_csv(zip_file.open(name), **kwargs), name)
                if df_transform
                else pd.read_csv(zip_file.open(name), **kwargs)
                for name in report_names
            ])
            merged_df.reset_index(drop=True, inplace=True)
            return merged_df

        return pd.read_csv(stream, **kwargs)


def extract_json_from_raw_zip_file(
    raw_file: bytes,
    df_transform: Callable[[pd.DataFrame, str], pd.DataFrame] | None = None,
    **kwargs,
) -> pd.DataFrame:
    """
    Extracts jsons if raw_file is Zip archive, or returns single raw file and parses it to the pandas Dataframe

    Args:
    raw_file: Report raw file
    df_transform: Function that is modifying each file before concat, f.e. adding file names to the data frame
    **kwargs: Arguments that are proxied to the pandas read_json function

    Returns:
    Resultant dataframe
    """
    with io.BytesIO(raw_file) as stream:
        if is_zip(raw_file):
            zip_file = ZipFile(stream)
            report_names = get_report_names(zip_file, ".json")
            if not len(report_names):
                return pd.DataFrame()
            return pd.concat([
                df_transform(pd.read_json(zip_file.open(name), **kwargs), name)
                if df_transform
                else pd.read_json(zip_file.open(name), **kwargs)
                for name in report_names
            ])
        else:
            raise FileExtractionError(message="Incorrect file format")


def extract_json_from_unzipped_raw_file(raw_file: bytes, **kwargs) -> pd.DataFrame:
    with io.BytesIO(raw_file) as stream:
        return pd.read_json(stream, **kwargs)


def extract_tsvs_from_raw_file(
    raw_file: bytes,
    df_transform: Callable[[pd.DataFrame, str], pd.DataFrame] | None = None,
    **kwargs,
) -> pd.DataFrame:
    """
    Extracts csvs if raw_file is Zip archive, or returns single raw file and parses it to the pandas Dataframe

    Args:
    raw_file: Report raw file
    df_transform: Function that is modifying each file before concat, f.e. adding file names to the data frame
    **kwargs: Arguments that are proxied to the pandas read_csv function

    Returns:
    Resultant dataframe
    """
    with io.BytesIO(raw_file) as stream:
        if is_zip(raw_file):
            zip_file = ZipFile(stream)
            report_names = get_report_names(zip_file, ".tsv")
            if not len(report_names):
                return pd.DataFrame()
            merged_df = pd.concat([
                df_transform(pd.read_csv(zip_file.open(name), **kwargs), name).fillna(
                    ""
                )
                if df_transform
                else pd.read_csv(zip_file.open(name), **kwargs).fillna("")
                for name in report_names
            ])
            merged_df.reset_index(drop=True, inplace=True)
            return merged_df

        return pd.read_csv(stream, **kwargs)


def get_manifest_data_from_raw_file(raw_file: bytes) -> dict:
    """
    Reading manifest data from zip archive and convert it to dict

    Args:
    raw_file: Report raw file

    Returns:
    Manifest data in dict
    """
    if is_zip(raw_file):
        with io.BytesIO(raw_file) as stream, ZipFile(stream) as zip_file:
            return read_manifest_from_zip(zip_file)
    raise ManifestFileNotFound


class BaseConverter(ABC):
    converted_report_cls: Type[BaseConvertedReport]

    def __init__(self, raw_report: ReportMetadataWithRawFile) -> None:
        self._raw_report = raw_report

    @abstractmethod
    def _convert(self) -> pd.DataFrame:
        """
        Converts a source-specific dataframe into a dataframe that conforms to the
        observation schema.
        """
        pass

    def _add_common_columns(self, result: pd.DataFrame) -> pd.DataFrame:
        return result

    def _normalize_transformed_data(self, result):
        return result

    @capture_span()
    def convert(self) -> BaseConvertedReport:
        result = self._convert()
        result = self._add_common_columns(result=result)
        result = self._normalize_transformed_data(result=result)
        result = result.reset_index(drop=True)
        if result.empty:
            return self.converted_report_cls.empty()
        result = result[self.converted_report_cls.model.to_schema().columns.keys()]
        # TODO: validate if date_from and date_to from metadata matches df and log warning if not!
        return self.converted_report_cls(df=pl.from_pandas(result))


class BaseSalesConverter(BaseConverter, ABC):
    converted_report_cls = SalesConvertedReport

    def _add_common_columns(self, result: pd.DataFrame) -> pd.DataFrame:
        if result.empty:
            return result

        result["hash_acquisition_properties"] = result[
            [
                "transaction_type",
                "tax_type",
                "sale_modificator",
                "acquisition_platform",
                "acquisition_origin",
                "iap_flag",
            ]
        ].apply(
            lambda row: hashlib.md5("".join(row).encode("utf-8")).hexdigest(),  # noqa: S324
            axis=1,
        )
        result["category"] = SaleCategoryClassifier.classify(
            result[
                [
                    "free_units",
                    "gross_returned",
                    "gross_sales",
                    "price_local",
                    "platform",
                    "portal",
                    "retailer_tag",
                    "sale_modificator",
                    "units_returned",
                    "units_sold",
                ]
            ]
        )
        return result


class BaseVisibilityConverter(BaseConverter, ABC):
    converted_report_cls = VisibilityConvertedReport


class BaseWishlistCohortsConverter(BaseConverter, ABC):
    converted_report_cls = WishlistCohortsConvertedReport


class BaseWishlistActionsConverter(BaseConverter, ABC):
    converted_report_cls = WishlistActionsConvertedReport


class BaseCumulativeWishlistSalesConverter(BaseConverter, ABC):
    converted_report_cls = CumulativeWishlistSalesConvertedReport


class BaseWishlistBalanceConverter(BaseConverter, ABC):
    converted_report_cls = WishlistBalanceConvertedReport


class BaseDiscountsConverter(BaseConverter, ABC):
    converted_report_cls = DiscountsConvertedReport


class BaseMonthlyActiveUsersConverter(BaseConverter, ABC):
    converted_report_cls = MonthlyActiveUsersConvertedReport
