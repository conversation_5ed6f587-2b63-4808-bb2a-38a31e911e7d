from core_silver.observation_converter.converters.daily_active_users_converter import (
    BaseDailyActiveUsersConverter,
)
from data_sdk.domain import DisplayPortal, Portal
from data_sdk.domain.stores import Store


class MicrosoftDailyActiveUsersConverter(BaseDailyActiveUsersConverter):
    """Microsoft-specific daily active users converter."""

    def _get_portal(self) -> Portal:
        """Return Microsoft portal."""
        return Portal.MICROSOFT

    def _get_display_portal(self) -> DisplayPortal:
        """Return Microsoft display portal."""
        return DisplayPortal.MICROSOFT

    def _get_store(self) -> Store:
        """Return Microsoft store."""
        return Store.MICROSOFT
