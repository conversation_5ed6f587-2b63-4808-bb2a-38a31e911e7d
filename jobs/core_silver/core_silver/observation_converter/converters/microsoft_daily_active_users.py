from core_silver.observation_converter.converters.daily_active_users_converter import (
    BaseDailyActiveUsersConverter,
)
from data_sdk.domain import DisplayPortal, Portal
from data_sdk.domain.stores import Store


class MicrosoftDailyActiveUsersConverter(BaseDailyActiveUsersConverter):
    """Microsoft-specific daily active users converter."""

    PORTAL = Portal.MICROSOFT
    DISPLAY_PORTAL = DisplayPortal.MICROSOFT
    STORE = Store.MICROSOFT
