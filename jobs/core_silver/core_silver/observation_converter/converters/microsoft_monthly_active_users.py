from zipfile import BadZipFile

import pandas as pd
import pandera as pa
from pandera import Column

from core_silver.observation_converter.common_columns import (
    generate_portal_platform_region,
    generate_unique_sku_id,
)
from core_silver.observation_converter.converters.base_converter import (
    BaseMonthlyActiveUsersConverter,
    extract_csvs_from_raw_file,
    get_manifest_data_from_raw_file,
)
from core_silver.observation_converter.converters.exceptions import (
    FileExtractionError,
    FileSchemaError,
    ManifestFileNotFound,
)
from core_silver.observation_converter.pandas_utils import InvalidSchema, enforce_schema
from data_sdk.domain import DisplayPortal, Portal
from data_sdk.domain.countries import get_country_alpha_3
from data_sdk.domain.platform import DisplayPlatform
from data_sdk.domain.regions import Region
from data_sdk.domain.stores import Store


class MicrosoftMonthlyActiveUsersConverter(BaseMonthlyActiveUsersConverter):
    _schema = pa.DataFrameSchema({
        "account": Column(pa.String, coerce=True),
        "portal": Column(pa.String, coerce=True),
        "year": Column(pa.Int, coerce=True),
        "month": Column(pa.Int, coerce=True),
        "product": Column(pa.String, coerce=True),
        "product_id": Column(pa.String, coerce=True),
        "count": Column(pa.Int, coerce=True),
    })

    def parse(self, raw_file: bytes) -> pd.DataFrame:
        df = self._extract_files(raw_file)
        df.sort_index(ascending=True, inplace=True)
        try:
            return enforce_schema(df, self._schema)
        except InvalidSchema as ex:
            raise FileSchemaError(str(ex))

    def _convert(self) -> pd.DataFrame:
        manifest = self._raw_report.metadata
        parsed_df = self.parse(self._raw_report.raw_file)

        if parsed_df.empty:
            return pd.DataFrame()

        converted_df = pd.DataFrame()
        converted_df = converted_df.assign(
            sku_id=parsed_df["product_id"],
            year=parsed_df["year"],
            month=parsed_df["month"],
            human_name=parsed_df["product"],
            monthly_active_users=parsed_df["count"],
            store_id=parsed_df["product_id"],
            platform=DisplayPlatform.PC,
            region=Region.GLOBAL,
            portal=DisplayPortal.MICROSOFT,
            store=Store.MICROSOFT.value,
            abbreviated_name=Store.MICROSOFT.abbreviate(),
            country_code=get_country_alpha_3("unknown"),
            report_id=manifest.report_id,
            studio_id=manifest.studio_id,
        )

        converted_df["portal_platform_region"] = generate_portal_platform_region(
            converted_df["portal"], converted_df["platform"], converted_df["region"]
        )
        converted_df["unique_sku_id"] = generate_unique_sku_id(
            converted_df, Portal.MICROSOFT
        )
        return converted_df

    def _extract_files(
        self, raw_file: bytes, file_list: list[str] | None = None
    ) -> pd.DataFrame:
        try:
            self.manifest_from_file = get_manifest_data_from_raw_file(raw_file)
            raw_df = extract_csvs_from_raw_file(
                raw_file,
                df_transform=None,
                file_list_filter=lambda filename: "monthly_" in filename,
                sep=",",
                header=0,
                parse_dates=False,  # We don't parse dates for MAU since we have year/month
                quotechar='"',
                keep_default_na=False,
                na_values=[""],
                dtype={
                    "account": str,
                    "portal": str,
                    "year": str,
                    "month": str,
                    "product": str,
                    "product_id": str,
                    "count": str,
                },
            )
        except (ValueError, BadZipFile, ManifestFileNotFound) as ex:
            raise FileExtractionError(str(ex))
        return raw_df
