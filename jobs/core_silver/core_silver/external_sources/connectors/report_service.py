from __future__ import annotations

import abc
import logging
from datetime import datetime
from functools import singledispatchmethod
from typing import Any

from core_silver.external_sources.connectors.api import (
    APIClient,
    APIConfig,
    StaticConfig,
)
from data_sdk.domain.domain_types import (
    ExternalSKU,
    ReportMetadata,
    ReportsMetadata,
    ReportState,
    StudioId,
)
from data_sdk.domain.source import Source

log = logging.getLogger(__name__)


REPORTS_TO_BE_PROCESSED_STATES = [
    state for state in ReportState if state != ReportState.DELETED
]


class ReportServiceClient(abc.ABC):
    @singledispatchmethod
    @staticmethod
    def get_client(config: Any) -> ReportServiceClient:
        raise ValueError("Invalid source type")  # noqa: TRY003

    @get_client.register
    @staticmethod
    def _(config: StaticConfig):
        return StaticReportServiceClient()

    @get_client.register
    @staticmethod
    def _(config: APIConfig):
        return APIReportServiceClient(config)

    @abc.abstractmethod
    def get_report_metadata(self, studio_id: StudioId) -> ReportsMetadata:
        pass

    @abc.abstractmethod
    def get_skus(self, studio_id: StudioId) -> list[ExternalSKU]:
        pass

    @abc.abstractmethod
    def send_sku(self, skus: list[dict[str, Any]], updated_skus: list[dict[str, Any]]):
        pass

    @abc.abstractmethod
    def send_reports(self, updated_reports: list[dict[str, Any]]):
        pass


class APIReportServiceClient(ReportServiceClient):
    def __init__(self, report_service_config: APIConfig):
        self._api = APIClient(
            base_url=report_service_config.url,
            extra_headers={
                "x-api-key": report_service_config.api_key,
            },
        )

    def get_report_metadata(self, studio_id: StudioId) -> ReportsMetadata:
        log.info(f"Getting report metadata for studio {studio_id}")
        manifests = self._api.get_paged(
            "reports",
            params={
                "studio_id": studio_id,
                "states": REPORTS_TO_BE_PROCESSED_STATES,
            },
        )
        ignored_sources = []
        sources_to_process = list(set(Source) - set(ignored_sources))

        unsupported_reports = [
            manifest
            for manifest in manifests
            if manifest["source"] not in sources_to_process
        ]

        for unsuppored_manifest in unsupported_reports:
            manifests.remove(unsuppored_manifest)

        return ReportsMetadata.model_validate(manifests)

    def get_skus(self, studio_id: StudioId) -> list[ExternalSKU]:
        log.info(f"Getting skus for studio {studio_id}")
        skus = self._api.get_paged(
            "skus",
            params={
                "studio_id": studio_id,
            },
        )
        return [ExternalSKU.model_validate(sku) for sku in skus]

    def send_sku(
        self, new_skus: list[dict[str, Any]], updated_skus: list[dict[str, Any]]
    ):
        if not new_skus and not updated_skus:
            log.info("No SKUs to send to Report Service")
            return

        log.info(
            f"Sending skus {new_skus} and updated_skus {updated_skus} to Report Service"
        )

        response = self._api.post(
            "skus",
            json={
                "skus": new_skus,
                "updated_skus": updated_skus,
            },
        )
        return response.raise_for_status()

    def send_reports(self, updated_reports: list[dict[str, Any]]):
        if not updated_reports:
            log.info("No reports to send to Report Service")
            return

        log.info(f"Sending reports {updated_reports} to Report Service")

        response = self._api.put(
            "reports/bulk",
            json={"updated": updated_reports},
        )
        return response.raise_for_status()


class StaticReportServiceClient(ReportServiceClient):
    def get_report_metadata(self, studio_id: StudioId) -> ReportsMetadata:
        return ReportsMetadata(
            root=[
                ReportMetadata(
                    source=Source.APP_STORE_SALES,
                    studio_id=studio_id,
                    report_id=1,  # type: ignore # type: ignore
                    upload_date=datetime.fromisoformat("2023-02-20 00:00:01.000000Z"),
                    blob_name="app_store_sales.zip",  # type: ignore # type: ignore
                    original_name="app_store_sales.zip",
                    date_from=datetime.fromisoformat("2023-01-01"),
                    date_to=datetime.fromisoformat("2023-01-04"),
                    state=ReportState.PENDING,
                    no_data=False,
                ),
                ReportMetadata(
                    source=Source.EPIC_SALES,
                    studio_id=studio_id,
                    report_id=2,  # type: ignore
                    upload_date=datetime.fromisoformat("2023-10-03 00:00:01.000000Z"),
                    blob_name="epic_with_positive_returns.zip",  # type: ignore
                    original_name="epic_with_positive_returns.zip",
                    date_from=datetime.fromisoformat("2020-01-01"),
                    date_to=datetime.fromisoformat("2020-01-31"),
                    state=ReportState.PENDING,
                    no_data=False,
                ),
                ReportMetadata(
                    source=Source.GOG_SALES,
                    studio_id=studio_id,
                    report_id=3,  # type: ignore
                    upload_date=datetime.fromisoformat("2022-01-14 00:00:01.000000Z"),
                    blob_name="GOG-2021-12-29_2022-01-14.zip",  # type: ignore
                    original_name="GOG-2021-12-29_2022-01-14.zip",
                    date_from=datetime.fromisoformat("2021-12-29"),
                    date_to=datetime.fromisoformat("2022-01-14"),
                    state=ReportState.PENDING,
                    no_data=False,
                ),
                ReportMetadata(
                    source=Source.GOOGLE_SALES,
                    studio_id=studio_id,
                    report_id=4,  # type: ignore
                    upload_date=datetime.fromisoformat("2023-02-20 00:00:01.000000Z"),
                    blob_name="google_sales_v.zip",  # type: ignore
                    original_name="google_sales_v.zip",
                    date_from=datetime.fromisoformat("2023-03-01"),
                    date_to=datetime.fromisoformat("2023-04-30"),
                    state=ReportState.PENDING,
                    no_data=False,
                ),
                ReportMetadata(
                    source=Source.HUMBLE_SALES,
                    studio_id=studio_id,
                    report_id=5,  # type: ignore
                    upload_date=datetime.fromisoformat("2021-06-17 00:00:00.000000Z"),
                    blob_name="humble_sales_report_20200528_20200531.csv",  # type: ignore
                    original_name="humble_sales_report_20200528_20200531.csv",
                    date_from=datetime.fromisoformat("2020-05-28"),
                    date_to=datetime.fromisoformat("2020-05-31"),
                    state=ReportState.PENDING,
                    no_data=False,
                ),
                ReportMetadata(
                    source=Source.META_QUEST_SALES,
                    studio_id=studio_id,
                    report_id=6,  # type: ignore
                    upload_date=datetime.fromisoformat("2021-01-28 00:00:01.000000Z"),
                    blob_name="META_QUEST-2021-07-14_2021-07-22.zip",  # type: ignore
                    original_name="META_QUEST-2021-07-14_2021-07-22.zip",
                    date_from=datetime.fromisoformat("2021-07-26"),
                    date_to=datetime.fromisoformat("2021-09-07"),
                    state=ReportState.PENDING,
                    no_data=False,
                ),
                ReportMetadata(
                    source=Source.META_RIFT_SALES,
                    studio_id=studio_id,
                    report_id=7,  # type: ignore
                    upload_date=datetime.fromisoformat("2021-01-28 00:00:01.000000Z"),
                    blob_name="META_RIFT-2021-08-07_2021-08-18.zip",  # type: ignore
                    original_name="META_RIFT-2021-08-07_2021-08-18.zip",
                    date_from=datetime.fromisoformat("2021-07-26"),
                    date_to=datetime.fromisoformat("2021-09-07"),
                    state=ReportState.PENDING,
                    no_data=False,
                ),
                ReportMetadata(
                    source=Source.MICROSOFT_SALES,
                    studio_id=studio_id,
                    report_id=8,  # type: ignore
                    upload_date=datetime.fromisoformat("2021-11-29 00:00:01.000000Z"),
                    blob_name="Microsoft_inapp_sku.zip",  # type: ignore
                    original_name="Microsoft_inapp_sku.zip",
                    date_from=datetime.fromisoformat("2021-05-03"),
                    date_to=datetime.fromisoformat("2021-11-01"),
                    state=ReportState.PENDING,
                    no_data=False,
                ),
                ReportMetadata(
                    source=Source.NINTENDO_SALES,
                    studio_id=studio_id,
                    report_id=9,  # type: ignore
                    upload_date=datetime.fromisoformat("2022-05-20 00:00:01.000000Z"),
                    blob_name="nintendo_sales-2022-05-01_2022-05-20.zip",  # type: ignore
                    original_name="nintendo_sales-2022-05-01_2022-05-20.zip",
                    date_from=datetime.fromisoformat("2022-05-01"),
                    date_to=datetime.fromisoformat("2022-05-20"),
                    state=ReportState.PENDING,
                    no_data=False,
                ),
                ReportMetadata(
                    source=Source.PLAYSTATION_SALES,
                    studio_id=studio_id,
                    report_id=10,  # type: ignore
                    upload_date=datetime.fromisoformat("2023-01-24 00:00:01.000000Z"),
                    blob_name="ps_api_psone.zip",  # type: ignore
                    original_name="ps_api_psone.zip",
                    date_from=datetime.fromisoformat("2023-01-08"),
                    date_to=datetime.fromisoformat("2023-01-12"),
                    state=ReportState.PENDING,
                    no_data=False,
                ),
                ReportMetadata(
                    source=Source.STEAM_SALES,
                    studio_id=studio_id,
                    report_id=11,  # type: ignore
                    upload_date=datetime.fromisoformat("2024-04-04T00:00:00.000000Z"),
                    file_path_raw="steam_sales-2024-04-04_2024-04-16.zip",  # type: ignore
                    original_name="steam_sales-2024-04-04_2024-04-16.zip",
                    date_from=datetime.fromisoformat("2024-04-04"),
                    date_to=datetime.fromisoformat("2024-04-16"),
                    state=ReportState.PENDING,
                    no_data=False,
                ),
                ReportMetadata(
                    source=Source.STEAM_IMPRESSIONS,
                    studio_id=studio_id,
                    report_id=12,  # type: ignore
                    upload_date=datetime.fromisoformat("2019-04-13 00:00:01.000000Z"),
                    file_path_raw="STEAM_IMPRESSIONS-2019-02-21_2019-04-13.zip",  # type: ignore
                    original_name="STEAM_IMPRESSIONS-2019-02-21_2019-04-13.zip",
                    date_from=datetime.fromisoformat("2019-02-21"),
                    date_to=datetime.fromisoformat("2019-04-13"),
                    state=ReportState.PENDING,
                    no_data=False,
                ),
                ReportMetadata(
                    source=Source.STEAM_DISCOUNTS,
                    studio_id=studio_id,
                    report_id=13,  # type: ignore
                    upload_date=datetime.fromisoformat("2023-10-16 00:00:01.000000Z"),
                    blob_name="steam_discounts_valid_missing_optinname_column.zip",  # type: ignore
                    original_name="steam_discounts_valid_missing_optinname_column.zip",
                    date_from=datetime.fromisoformat("2010-01-01"),
                    date_to=datetime.fromisoformat("2023-10-16"),
                    state=ReportState.PENDING,
                    no_data=False,
                ),
                ReportMetadata(
                    source=Source.NINTENDO_DISCOUNTS,
                    studio_id=studio_id,
                    report_id=14,  # type: ignore
                    upload_date=datetime.fromisoformat("2023-10-15 00:00:01.000000Z"),
                    blob_name="nintendo_discounts_v.zip",  # type: ignore
                    original_name="nintendo_discounts_v.zip",
                    date_from=datetime.fromisoformat("2010-01-01"),
                    date_to=datetime.fromisoformat("2023-10-15"),
                    state=ReportState.PENDING,
                    no_data=False,
                ),
                ReportMetadata(
                    source=Source.STEAM_WISHLIST_ACTIONS,
                    studio_id=studio_id,
                    report_id=15,  # type: ignore
                    upload_date=datetime.fromisoformat("2021-01-28 00:00:01.000000Z"),
                    blob_name="STEAM_WISHLISTS-actions_valid.zip",  # type: ignore
                    original_name="STEAM_WISHLISTS-actions_valid.zip",
                    date_from=datetime.fromisoformat("2021-08-26"),
                    date_to=datetime.fromisoformat("2021-10-07"),
                    state=ReportState.PENDING,
                    no_data=False,
                ),
                ReportMetadata(
                    source=Source.STEAM_WISHLIST_COHORTS,
                    studio_id=studio_id,
                    report_id=16,  # type: ignore
                    upload_date=datetime.fromisoformat("2021-01-28 00:00:01.000000Z"),
                    blob_name="STEAM_WISHLISTS-cohorts_valid.zip",  # type: ignore
                    original_name="STEAM_WISHLISTS-cohorts_valid.zip",
                    date_from=datetime.fromisoformat("2021-08-26"),
                    date_to=datetime.fromisoformat("2021-10-07"),
                    state=ReportState.PENDING,
                    no_data=False,
                ),
                ReportMetadata(
                    source=Source.PLAYSTATION_WISHLIST_ACTIONS,
                    studio_id=studio_id,
                    report_id=17,  # type: ignore
                    upload_date=datetime.fromisoformat("2023-07-22 00:00:01.00000Z"),
                    blob_name="PS_WISHLISTS-actions_valid.zip",  # type: ignore
                    original_name="PS_WISHLISTS-actions_valid.zip",
                    date_from=datetime.fromisoformat("2023-07-21"),
                    date_to=datetime.fromisoformat("2023-07-21"),
                    state=ReportState.PENDING,
                    no_data=False,
                ),
                ReportMetadata(
                    source=Source.NINTENDO_WISHLIST_ACTIONS,
                    studio_id=studio_id,
                    report_id=18,  # type: ignore
                    upload_date=datetime.fromisoformat("2023-02-23 00:00:01.00000Z"),
                    blob_name="nintendo_wishlist_actions_valid.zip",  # type: ignore
                    original_name="nintendo_wishlist_actions_valid.zip",
                    date_from=datetime.fromisoformat("2023-01-14"),
                    date_to=datetime.fromisoformat("2023-02-23"),
                    state=ReportState.PENDING,
                    no_data=False,
                ),
                ReportMetadata(
                    source=Source.NINTENDO_CUMULATIVE_WISHLIST_SALES,
                    studio_id=studio_id,
                    report_id=19,  # type: ignore
                    upload_date=datetime.fromisoformat("2023-02-23 00:00:01.00000Z"),
                    blob_name="nintendo_wishlist_actions_valid.zip",  # type: ignore
                    original_name="nintendo_wishlist_actions_valid.zip",
                    date_from=datetime.fromisoformat("2023-01-14"),
                    date_to=datetime.fromisoformat("2023-02-23"),
                    state=ReportState.PENDING,
                    no_data=False,
                ),
                ReportMetadata(
                    source=Source.STEAM_WISHLIST_BALANCE,
                    studio_id=studio_id,
                    report_id=20,  # type: ignore
                    upload_date=datetime.fromisoformat("2025-05-03 00:00:01.000000Z"),
                    blob_name="steam_wishlist_balance_v.zip",  # type: ignore
                    original_name="steam_wishlist_balance_v.zip",
                    date_from=datetime.fromisoformat("2025-05-01"),
                    date_to=datetime.fromisoformat("2025-05-02"),
                    state=ReportState.PENDING,
                    no_data=False,
                ),
                ReportMetadata(
                    source=Source.MICROSOFT_DAILY_ACTIVE_USERS,
                    studio_id=studio_id,
                    report_id=21,  # type: ignore
                    upload_date=datetime.fromisoformat("2025-07-01 00:00:01.00000Z"),
                    blob_name="dau/microsoft_daily_active_users_superhot_2025-06.zip",  # type: ignore
                    original_name="dau/microsoft_daily_active_users_superhot_2025-06.zip",
                    date_from=datetime.fromisoformat("2025-06-01"),
                    date_to=datetime.fromisoformat("2025-06-30"),
                    state=ReportState.PENDING,
                    no_data=False,
                ),
                ReportMetadata(
                    source=Source.STEAM_DAILY_ACTIVE_USERS,
                    studio_id=studio_id,
                    report_id=22,  # type: ignore
                    upload_date=datetime.fromisoformat("2025-06-03 00:00:01.00000Z"),
                    blob_name="dau/steam_daily_active_users_superhot_2025-01.zip",  # type: ignore
                    original_name="dau/steam_daily_active_users_superhot_2025-01.zip",
                    date_from=datetime.fromisoformat("2025-01-01"),
                    date_to=datetime.fromisoformat("2025-01-31"),
                    state=ReportState.PENDING,
                    no_data=False,
                ),
            ]
        )

    def get_skus(self, studio_id: StudioId) -> list[ExternalSKU]:
        skus = [
            {
                "store_id": "Unknown",
                "sku_studio": f"115109-steam:{studio_id}",
                "studio_id": studio_id,
                "product_name": None,
                "custom_group": None,
                "ratio": 1,
                "base_sku_id": "115109",
                "human_name": "Dimension Hunter for Beta Testing (retail)",
                "package_name": None,
                "product_type": None,
                "portal": "steam",
                "sku_type": "SALES",
                "human_name_indicator": "sales",
                "is_discountable": True,
            },
            {
                "store_id": "Unknown",
                "sku_studio": f"115110-steam:{studio_id}",
                "studio_id": studio_id,
                "product_name": None,
                "custom_group": None,
                "ratio": 1,
                "base_sku_id": "115110",
                "human_name": "Dimension Hunter",
                "package_name": None,
                "product_type": None,
                "portal": "steam",
                "sku_type": "SALES",
                "human_name_indicator": "sales",
                "is_discountable": False,
            },
            {
                "store_id": "1380126",
                "sku_studio": f"999110-store:{studio_id}",
                "studio_id": studio_id,
                "product_name": None,
                "custom_group": None,
                "ratio": 1,
                "base_sku_id": "999110",
                "human_name": "Dimension Hunter - Complete Edition",
                "package_name": None,
                "product_type": None,
                "portal": "steam",
                "sku_type": "STORE",
                "human_name_indicator": "visibility",
                "is_discountable": False,
            },
        ]
        return [ExternalSKU.model_validate(sku) for sku in skus]

    def send_sku(self, skus: list[dict[str, Any]], updated_skus: list[dict[str, Any]]):
        print("Sent skus:", skus)  # noqa: T201
        print("Sent updated skus:", updated_skus)  # noqa: T201

    def send_reports(self, updated_reports: list[dict[str, Any]]):
        print("Sent reports:", updated_reports)  # noqa: T201
