import pytest

from core_silver.observation_converter.converters.exceptions import (
    FileExtractionError,
    FileSchemaError,
)
from core_silver.observation_converter.converters.microsoft_monthly_active_users import (
    MicrosoftMonthlyActiveUsersConverter,
)


def test_convert_for_monthly_active_users_run(
    microsoft_monthly_active_users_metadata_with_raw_file_factory,
):
    metadata_with_raw_file = (
        microsoft_monthly_active_users_metadata_with_raw_file_factory(
            input_raw_file__start_year=2024,
            input_raw_file__start_month=6,
            input_raw_file__end_year=2024,
            input_raw_file__end_month=6,
            input_raw_file__rows__product="SUPERHOT WINDOWS 10",
            input_raw_file__rows__product_id="9NV17MJB26PG",
            input_raw_file__rows__count=1500,
            metadata__studio_id=123,
            metadata__report_id=456,
        )
    )

    converter = MicrosoftMonthlyActiveUsersConverter(metadata_with_raw_file)
    result = converter.convert()

    assert not result.df.is_empty()
    assert result.df.height == 1

    row = result.df.row(0, named=True)
    assert row["sku_id"] == "9NV17MJB26PG"
    assert row["year"] == 2024
    assert row["month"] == 6
    assert row["human_name"] == "SUPERHOT WINDOWS 10"
    assert row["monthly_active_users"] == 1500
    assert row["store_id"] == "9NV17MJB26PG"
    assert row["platform"] == "PC"
    assert row["region"] == "Global"
    assert row["portal"] == "Microsoft"
    assert row["store"] == "Microsoft"
    assert row["abbreviated_name"] == "Microsoft"
    assert row["country_code"] == "ZZZ"
    assert row["report_id"] == 456
    assert row["studio_id"] == 123
    assert row["unique_sku_id"] == "9NV17MJB26PG-microsoft:123"
    assert row["portal_platform_region"] == "Microsoft:PC:Global"


def test_convert_for_monthly_active_users_run_empty(
    microsoft_monthly_active_users_metadata_with_raw_file_factory,
):
    metadata_with_raw_file = (
        microsoft_monthly_active_users_metadata_with_raw_file_factory(
            input_raw_file__rows=[],
        )
    )

    converter = MicrosoftMonthlyActiveUsersConverter(metadata_with_raw_file)
    result = converter.convert()

    assert result.df.is_empty()


def test_convert_for_multiple_months_and_products(
    microsoft_monthly_active_users_metadata_with_raw_file_factory,
):
    # Create custom rows for multiple months and products
    from tests.converters.microsoft_monthly_active_users.conftest import (
        MicrosoftMonthlyActiveUsersRowFactory,
    )

    rows = [
        MicrosoftMonthlyActiveUsersRowFactory(
            year=2024, month=5, product="SUPERHOT", product_id="9P2C5R4H5Z9S", count=800
        ),
        MicrosoftMonthlyActiveUsersRowFactory(
            year=2024,
            month=5,
            product="SUPERHOT VR",
            product_id="9NT60N3XPF7T",
            count=200,
        ),
        MicrosoftMonthlyActiveUsersRowFactory(
            year=2024, month=6, product="SUPERHOT", product_id="9P2C5R4H5Z9S", count=850
        ),
        MicrosoftMonthlyActiveUsersRowFactory(
            year=2024,
            month=6,
            product="SUPERHOT VR",
            product_id="9NT60N3XPF7T",
            count=220,
        ),
    ]

    metadata_with_raw_file = (
        microsoft_monthly_active_users_metadata_with_raw_file_factory(
            input_raw_file__start_year=2024,
            input_raw_file__start_month=5,
            input_raw_file__end_year=2024,
            input_raw_file__end_month=6,
            input_raw_file__rows=rows,
            metadata__studio_id=123,
            metadata__report_id=456,
        )
    )

    converter = MicrosoftMonthlyActiveUsersConverter(metadata_with_raw_file)
    result = converter.convert()

    assert not result.df.is_empty()
    assert result.df.height == 4

    # Check that we have data for both months and both products
    df_pandas = result.df.to_pandas()

    # Check May data
    may_data = df_pandas[(df_pandas["year"] == 2024) & (df_pandas["month"] == 5)]
    assert len(may_data) == 2
    assert set(may_data["human_name"]) == {"SUPERHOT", "SUPERHOT VR"}
    assert set(may_data["monthly_active_users"]) == {800, 200}

    # Check June data
    june_data = df_pandas[(df_pandas["year"] == 2024) & (df_pandas["month"] == 6)]
    assert len(june_data) == 2
    assert set(june_data["human_name"]) == {"SUPERHOT", "SUPERHOT VR"}
    assert set(june_data["monthly_active_users"]) == {850, 220}


def test_parse_invalid_file_raises_file_extraction_error(
    microsoft_monthly_active_users_metadata_with_raw_file_factory,
):
    metadata_with_raw_file = (
        microsoft_monthly_active_users_metadata_with_raw_file_factory()
    )
    metadata_with_raw_file.raw_file = b"invalid zip content"

    converter = MicrosoftMonthlyActiveUsersConverter(metadata_with_raw_file)

    with pytest.raises(FileExtractionError):
        converter.parse(metadata_with_raw_file.raw_file)


def test_parse_invalid_schema_raises_file_schema_error(
    microsoft_monthly_active_users_metadata_with_raw_file_factory,
):
    # Create a file with invalid schema (missing required columns)
    from tests.utils import _mock_zip_file

    invalid_csv_content = {
        "manifest.json": {
            "dateFrom": "2024-06-01",
            "dateTo": "2024-06-30",
            "fileMetaData": {
                "monthly_2024-06-01_2024-06-30.csv": {
                    "dateFrom": "2024-06-01",
                    "dateTo": "2024-06-30",
                }
            },
        },
        "monthly_2024-06-01_2024-06-30.csv": "invalid,header,structure\ndata,without,proper,columns\n",
    }

    with _mock_zip_file(invalid_csv_content) as zip_file:
        invalid_raw_file = zip_file.fp.getvalue()

    metadata_with_raw_file = (
        microsoft_monthly_active_users_metadata_with_raw_file_factory()
    )
    converter = MicrosoftMonthlyActiveUsersConverter(metadata_with_raw_file)

    with pytest.raises(FileSchemaError):
        converter.parse(invalid_raw_file)
