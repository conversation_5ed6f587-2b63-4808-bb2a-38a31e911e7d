def test_generate_empty_microsoft_monthly_active_users(
    microsoft_raw_monthly_active_users_factory,
):
    microsoft_raw_mau = microsoft_raw_monthly_active_users_factory(
        start_year=2023, start_month=10, end_year=2023, end_month=10, rows=[]
    )
    assert microsoft_raw_mau.start_year == 2023
    assert microsoft_raw_mau.start_month == 10
    assert microsoft_raw_mau.end_year == 2023
    assert microsoft_raw_mau.end_month == 10
    assert microsoft_raw_mau.manifest_json == {
        "manifest.json": {
            "dateFrom": "2023-10-01",
            "dateTo": "2023-10-28",
            "fileMetaData": {
                "monthly_2023-10-01_2023-10-28.csv": {
                    "dateFrom": "2023-10-01",
                    "dateTo": "2023-10-28",
                },
            },
        },
    }
    assert microsoft_raw_mau.monthly_active_users_csv == {
        "monthly_2023-10-01_2023-10-28.csv": "account,portal,year,month,product,product_id,count\n",
    }


def test_generate_one_month_of_microsoft_monthly_active_users(
    microsoft_raw_monthly_active_users_factory,
):
    result = microsoft_raw_monthly_active_users_factory(
        start_year=2024,
        start_month=4,
        end_year=2024,
        end_month=4,
    )

    assert result.start_year == 2024
    assert result.start_month == 4
    assert result.end_year == 2024
    assert result.end_month == 4
    assert result.monthly_active_users_csv == {
        "monthly_2024-04-01_2024-04-28.csv": (
            "account,portal,year,month,product,product_id,count\n"
            "Test account,microsoft,2024,4,Test product,9NV17MJB26PG,100\n"
        ),
    }


def test_generate_microsoft_monthly_active_users_with_custom_data(
    microsoft_raw_monthly_active_users_factory,
):
    result = microsoft_raw_monthly_active_users_factory(
        start_year=2023,
        start_month=10,
        end_year=2023,
        end_month=10,
        rows__product="SUPERHOT WINDOWS 10",
        rows__product_id="9NV17MJB26PG",
        rows__count=1500,
    )

    assert result.start_year == 2023
    assert result.start_month == 10
    assert result.end_year == 2023
    assert result.end_month == 10
    assert result.monthly_active_users_csv == {
        "monthly_2023-10-01_2023-10-28.csv": (
            "account,portal,year,month,product,product_id,count\n"
            "Test account,microsoft,2023,10,SUPERHOT WINDOWS 10,9NV17MJB26PG,1500\n"
        ),
    }


def test_generate_multiple_months_of_microsoft_monthly_active_users(
    microsoft_raw_monthly_active_users_factory,
):
    result = microsoft_raw_monthly_active_users_factory(
        start_year=2024,
        start_month=3,
        end_year=2024,
        end_month=5,
        rows__size=3,  # 3 months: March, April, May
    )

    assert result.start_year == 2024
    assert result.start_month == 3
    assert result.end_year == 2024
    assert result.end_month == 5

    # Check that we have 3 rows
    assert len(result.rows) == 3

    # Check the CSV content contains all 3 months
    csv_content = result.monthly_active_users_csv["monthly_2024-03-01_2024-05-28.csv"]
    assert "Test account,microsoft,2024,3,Test product,9NV17MJB26PG,100" in csv_content
    assert "Test account,microsoft,2024,4,Test product,9NV17MJB26PG,100" in csv_content
    assert "Test account,microsoft,2024,5,Test product,9NV17MJB26PG,100" in csv_content


def test_microsoft_monthly_active_users_can_be_added_together(
    microsoft_raw_monthly_active_users_factory,
):
    first_month = microsoft_raw_monthly_active_users_factory(
        start_year=2024, start_month=1, end_year=2024, end_month=1
    )
    second_month = microsoft_raw_monthly_active_users_factory(
        start_year=2024, start_month=2, end_year=2024, end_month=2
    )

    combined = first_month + second_month

    assert combined.start_year == 2024
    assert combined.start_month == 1
    assert combined.end_year == 2024
    assert combined.end_month == 2
    assert len(combined.rows) == 2
