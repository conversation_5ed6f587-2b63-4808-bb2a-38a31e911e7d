from datetime import date

import factory
import pytest
from attr import dataclass

from data_sdk.domain import Portal
from data_sdk.domain.domain_types import ReportMetadataWithRawFile
from data_sdk.domain.observations import ObservationType
from data_sdk.domain.source import Source
from tests.conftest import ExternalReportsFactory, ListBetterSubFactory, ListSubFactory
from tests.utils import _mock_zip_file


class MicrosoftMonthlyActiveUsersRowFactory(factory.StubFactory):
    class Params:
        start_year = 2024
        start_month = 1

    year = factory.LazyAttributeSequence(
        lambda o, n: o.start_year + (o.start_month + n - 1) // 12
    )
    month = factory.LazyAttributeSequence(lambda o, n: (o.start_month + n - 1) % 12 + 1)

    account = "Test account"
    portal = "microsoft"
    product = "Test product"
    product_id = "9NV17MJB26PG"
    count = 100

    @factory.lazy_attribute
    def csv_row(self):
        return f"{self.account},{self.portal},{self.year},{self.month},{self.product},{self.product_id},{self.count}\n"


@dataclass
class MicrosoftRawMonthlyActiveUsers:
    start_year: int
    start_month: int
    end_year: int
    end_month: int
    rows: list

    @property
    def manifest_json(self):
        return {
            "manifest.json": {
                "dateFrom": f"{self.start_year}-{self.start_month:02d}-01",
                "dateTo": f"{self.end_year}-{self.end_month:02d}-28",
                "fileMetaData": {
                    f"monthly_{self.start_year}-{self.start_month:02d}-01_{self.end_year}-{self.end_month:02d}-28.csv": {
                        "dateFrom": f"{self.start_year}-{self.start_month:02d}-01",
                        "dateTo": f"{self.end_year}-{self.end_month:02d}-28",
                    },
                },
            }
        }

    @property
    def monthly_active_users_csv(self):
        csvs = {}

        # Add monthly file
        monthly_header = "account,portal,year,month,product,product_id,count\n"
        monthly_filename = f"monthly_{self.start_year}-{self.start_month:02d}-01_{self.end_year}-{self.end_month:02d}-28.csv"
        csvs[monthly_filename] = monthly_header
        for row in self.rows:
            csvs[monthly_filename] += row.csv_row

        return csvs

    @property
    def zip_content(self):
        return {**self.manifest_json, **self.monthly_active_users_csv}

    @property
    def raw_zip_file(self):
        with _mock_zip_file(self.zip_content) as zip_file:
            return zip_file.fp.getvalue()

    def __add__(self, other):
        return MicrosoftRawMonthlyActiveUsersFactory(
            start_year=self.start_year,
            start_month=self.start_month,
            end_year=other.end_year,
            end_month=other.end_month,
            rows=self.rows + other.rows,
        )


class MicrosoftRawMonthlyActiveUsersFactory(factory.Factory):
    class Meta:
        model = MicrosoftRawMonthlyActiveUsers

    start_year = 2024
    start_month = 4
    end_year = 2024
    end_month = 4

    rows = ListSubFactory(
        MicrosoftMonthlyActiveUsersRowFactory,
        size=lambda o: (o.end_year - o.start_year) * 12
        + (o.end_month - o.start_month)
        + 1,
        start_year=factory.LazyAttribute(lambda row: row.factory_parent.start_year),
        start_month=factory.LazyAttribute(lambda row: row.factory_parent.start_month),
    )


@pytest.fixture
def microsoft_raw_monthly_active_users_factory():
    return MicrosoftRawMonthlyActiveUsersFactory


@pytest.fixture
def external_microsoft_monthly_active_users_reports_factory():
    class ExternalMicrosoftMonthlyActiveUsersReportsFactory(ExternalReportsFactory):
        source = Source.MICROSOFT_MONTHLY_ACTIVE_USERS
        portal = Portal.MICROSOFT
        observation_type = ObservationType.MONTHLY_ACTIVE_USERS

    return ExternalMicrosoftMonthlyActiveUsersReportsFactory


@pytest.fixture
def microsoft_monthly_active_users_metadata_with_raw_file_factory(
    microsoft_raw_monthly_active_users_factory,
    external_microsoft_monthly_active_users_reports_factory,
):
    class MicrosoftMonthlyActiveUsersReportMetadataWithRawFileFactory(factory.Factory):
        class Meta:
            model = ReportMetadataWithRawFile

        input_raw_file = factory.SubFactory(microsoft_raw_monthly_active_users_factory)
        metadata = factory.SubFactory(
            external_microsoft_monthly_active_users_reports_factory
        )

        @factory.lazy_attribute
        def raw_file(self):
            return self.input_raw_file.raw_zip_file

    return MicrosoftMonthlyActiveUsersReportMetadataWithRawFileFactory
