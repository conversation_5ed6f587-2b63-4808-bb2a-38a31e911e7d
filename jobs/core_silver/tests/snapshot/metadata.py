from datetime import datetime

from data_sdk.domain.domain_types import ReportMetadata, ReportState, StudioId
from data_sdk.domain.source import Source

STUDIO_ID = StudioId(1)

app_store_sales_test_metadata = [
    ReportMetadata(
        source=Source.APP_STORE_SALES,
        studio_id=STUDIO_ID,
        report_id=432,  # type: ignore
        upload_date=datetime.fromisoformat("2023-02-20 00:00:01.00000Z"),
        blob_name="app_store_sales.zip",  # type: ignore
        original_name="app_store_sales.zip",
        date_from=datetime.fromisoformat("2023-01-01"),
        date_to=datetime.fromisoformat("2023-01-04"),
        state=ReportState.PENDING,
        no_data=False,
    ),
    ReportMetadata(
        source=Source.APP_STORE_SALES,
        studio_id=STUDIO_ID,
        report_id=432,  # type: ignore
        upload_date=datetime.fromisoformat("2023-02-20 00:00:01.00000Z"),
        blob_name="app_store_sales_empty_tsv.zip",  # type: ignore
        original_name="app_store_sales_empty_tsv.zip",
        date_from=datetime.fromisoformat("2023-01-01"),
        date_to=datetime.fromisoformat("2023-01-05"),
        state=ReportState.PENDING,
        no_data=False,
    ),
]
epic_sales_test_metadata = [
    ReportMetadata(
        source=Source.EPIC_SALES,
        studio_id=STUDIO_ID,
        report_id=21,  # type: ignore
        upload_date=datetime.fromisoformat("2021-11-03 00:00:01.00000Z"),
        blob_name="EPIC-2021-05-23_2021-11-03.csv",  # type: ignore
        original_name="EPIC-2021-05-23_2021-11-03.csv",
        date_from=datetime.fromisoformat("2021-05-23"),
        date_to=datetime.fromisoformat("2021-11-03"),
        state=ReportState.PENDING,
        no_data=False,
    ),
    ReportMetadata(
        source=Source.EPIC_SALES,
        studio_id=STUDIO_ID,
        report_id=21,  # type: ignore
        upload_date=datetime.fromisoformat("2023-10-03 00:00:01.00000Z"),
        blob_name="epic_with_positive_returns.zip",  # type: ignore
        original_name="epic_with_positive_returns.zip",
        date_from=datetime.fromisoformat("2020-01-01"),
        date_to=datetime.fromisoformat("2020-01-31"),
        state=ReportState.PENDING,
        no_data=False,
    ),
    ReportMetadata(
        source=Source.EPIC_SALES,
        studio_id=STUDIO_ID,
        report_id=34,  # type: ignore
        upload_date=datetime.fromisoformat("2021-06-17 00:00:01.00000Z"),
        blob_name="EPIC-free-units.csv",  # type: ignore
        original_name="EPIC-free-units.csv",
        date_from=datetime.fromisoformat("2020-11-23"),
        date_to=datetime.fromisoformat("2020-11-29"),
        state=ReportState.PENDING,
        no_data=False,
    ),
]
gog_sales_test_metadata = [
    ReportMetadata(
        source=Source.GOG_SALES,
        studio_id=STUDIO_ID,
        report_id=24,  # type: ignore
        upload_date=datetime.fromisoformat("2022-01-14 00:00:01.00000Z"),
        blob_name="GOG-2021-12-29_2022-01-14.zip",  # type: ignore
        original_name="GOG-2021-12-29_2022-01-14.zip",
        date_from=datetime.fromisoformat("2021-12-29"),
        date_to=datetime.fromisoformat("2022-01-14"),
        state=ReportState.PENDING,
        no_data=False,
    ),
    ReportMetadata(
        source=Source.GOG_SALES,
        studio_id=STUDIO_ID,
        report_id=35,  # type: ignore
        upload_date=datetime.fromisoformat("2022-01-14 00:00:01.00000Z"),
        blob_name="GOG-2021-12-29_2022-01-14-sku-manifest.zip",  # type: ignore
        original_name="GOG-2021-12-29_2022-01-14-sku-manifest.zip",
        date_from=datetime.fromisoformat("2021-12-29"),
        date_to=datetime.fromisoformat("2022-01-14"),
        state=ReportState.PENDING,
        no_data=False,
    ),
    ReportMetadata(
        source=Source.GOG_SALES,
        studio_id=STUDIO_ID,
        report_id=429,  # type: ignore
        upload_date=datetime.fromisoformat("2022-02-02 00:00:01.00000Z"),
        blob_name="gog_json_v.zip",  # type: ignore
        original_name="gog_json_v.zip",
        date_from=datetime.fromisoformat("2022-01-01"),
        date_to=datetime.fromisoformat("2022-02-01"),
        state=ReportState.PENDING,
        no_data=False,
    ),
    ReportMetadata(
        source=Source.GOG_SALES,
        studio_id=STUDIO_ID,
        report_id=324357,  # type: ignore
        upload_date=datetime.fromisoformat("2022-01-14 00:00:01.00000Z"),
        blob_name="gog_sales-2021-12-29_2022-01-14_manifest_v3.zip",  # type: ignore
        original_name="gog_sales-2021-12-29_2022-01-14_manifest_v3.zip",
        date_from=datetime.fromisoformat("2021-12-29"),
        date_to=datetime.fromisoformat("2022-01-14"),
        state=ReportState.PENDING,
        no_data=False,
    ),
    ReportMetadata(
        source=Source.GOG_SALES,
        studio_id=STUDIO_ID,
        report_id=324358,  # type: ignore
        upload_date=datetime.fromisoformat("2022-01-14 00:00:01.00000Z"),
        blob_name="gog_sales-2024-05-22_2024-06-18_v3_no_shares.zip",  # type: ignore
        original_name="gog_sales-2024-05-22_2024-06-18_v3_no_shares.zip",
        date_from=datetime.fromisoformat("2021-12-29"),
        date_to=datetime.fromisoformat("2022-01-14"),
        state=ReportState.PENDING,
        no_data=False,
    ),
    ReportMetadata(
        source=Source.GOG_SALES,
        studio_id=STUDIO_ID,
        report_id=324359,  # type: ignore
        upload_date=datetime.fromisoformat("2021-03-31 00:00:01.00000Z"),
        blob_name="gog_sales-2021-03-27_2021-03-30_v3_two_timezones.zip",  # type: ignore
        original_name="gog_sales-2021-03-27_2021-03-30_v3_two_timezones.zip",
        date_from=datetime.fromisoformat("2021-03-27"),
        date_to=datetime.fromisoformat("2021-03-30"),
        state=ReportState.PENDING,
        no_data=False,
    ),
]
google_sales_test_metadata = [
    ReportMetadata(
        source=Source.GOOGLE_SALES,
        studio_id=STUDIO_ID,
        report_id=434,  # type: ignore
        upload_date=datetime.fromisoformat("2023-02-20 00:00:01.00000Z"),
        blob_name="google_sales_v.zip",  # type: ignore
        original_name="google_sales_v.zip",
        date_from=datetime.fromisoformat("2023-03-01"),
        date_to=datetime.fromisoformat("2023-04-30"),
        state=ReportState.PENDING,
        no_data=False,
    ),
    ReportMetadata(
        source=Source.GOOGLE_SALES,
        studio_id=STUDIO_ID,
        report_id=436,  # type: ignore
        upload_date=datetime.fromisoformat("2023-02-20 00:00:01.00000Z"),
        blob_name="google_sales_less_cols_v.zip",  # type: ignore
        original_name="google_sales_less_cols_v.zip",
        date_from=datetime.fromisoformat("2015-01-01"),
        date_to=datetime.fromisoformat("2015-01-31"),
        state=ReportState.PENDING,
        no_data=False,
    ),
]
humble_sales_test_metadata = [
    ReportMetadata(
        source=Source.HUMBLE_SALES,
        studio_id=STUDIO_ID,
        report_id=24,  # type: ignore
        upload_date=datetime.fromisoformat("2021-06-17 00:00:00.00000Z"),
        blob_name="humble_sales_report_20200528_20200531.csv",  # type: ignore
        original_name="humble_sales_report_20200528_20200531.csv",
        date_from=datetime.fromisoformat("2020-05-28"),
        date_to=datetime.fromisoformat("2020-05-31"),
        state=ReportState.PENDING,
        no_data=False,
    ),
    ReportMetadata(
        source=Source.HUMBLE_SALES,
        studio_id=492,
        report_id=24,  # type: ignore
        upload_date=datetime.fromisoformat("2021-06-17 00:00:00.00000Z"),
        blob_name="humble_filter_sku.csv",  # type: ignore
        original_name="humble_filter_sku.csv",
        date_from=datetime.fromisoformat("2020-05-28"),
        date_to=datetime.fromisoformat("2020-05-31"),
        state=ReportState.PENDING,
        no_data=False,
    ),
    # gross price is almost 0 but not quite
    ReportMetadata(
        source=Source.HUMBLE_SALES,
        studio_id=STUDIO_ID,
        report_id=29,  # type: ignore
        upload_date=datetime.fromisoformat("2016-06-12 00:00:00.00000Z"),
        blob_name="humble_almost_free.csv",  # type: ignore
        original_name="humble_almost_free.csv",
        date_from=datetime.fromisoformat("2016-06-10"),
        date_to=datetime.fromisoformat("2016-06-10"),
        state=ReportState.PENDING,
        no_data=False,
    ),
]
meta_quest_sales_test_metadata = [
    ReportMetadata(
        source=Source.META_QUEST_SALES,
        studio_id=STUDIO_ID,
        report_id=21,  # type: ignore
        upload_date=datetime.fromisoformat("2021-01-28 00:00:01.00000Z"),
        blob_name="META_QUEST-2021-07-14_2021-07-22.zip",  # type: ignore
        original_name="META_QUEST-2021-07-14_2021-07-22.zip",
        date_from=datetime.fromisoformat("2021-07-26"),
        date_to=datetime.fromisoformat("2021-09-07"),
        state=ReportState.PENDING,
        no_data=False,
    ),
    ReportMetadata(
        source=Source.META_QUEST_SALES,
        studio_id=STUDIO_ID,
        report_id=22,  # type: ignore
        upload_date=datetime.fromisoformat("2021-01-28 00:00:01.00000Z"),
        blob_name="META_QUEST-2020-07-29_2021-07-29_free.zip",  # type: ignore
        original_name="META_QUEST-2020-07-29_2021-07-29_free.zip",
        date_from=datetime.fromisoformat("2021-07-26"),
        date_to=datetime.fromisoformat("2021-09-07"),
        state=ReportState.PENDING,
        no_data=False,
    ),
]
meta_rift_sales_test_metadata = [
    ReportMetadata(
        source=Source.META_RIFT_SALES,
        studio_id=STUDIO_ID,
        report_id=23,  # type: ignore
        upload_date=datetime.fromisoformat("2021-01-28 00:00:01.00000Z"),
        blob_name="META_RIFT-2021-08-07_2021-08-18.zip",  # type: ignore
        original_name="META_RIFT-2021-08-07_2021-08-18.zip",
        date_from=datetime.fromisoformat("2021-07-26"),
        date_to=datetime.fromisoformat("2021-09-07"),
        state=ReportState.PENDING,
        no_data=False,
    ),
    ReportMetadata(
        source=Source.META_RIFT_SALES,
        studio_id=STUDIO_ID,
        report_id=99997,  # type: ignore
        upload_date=datetime.fromisoformat("2022-01-21 00:00:01.00000Z"),
        blob_name="meta_quest_promo.zip",  # type: ignore
        original_name="meta_quest_promo.zip",
        date_from=datetime.fromisoformat("2021-12-09"),
        date_to=datetime.fromisoformat("2022-01-03"),
        state=ReportState.PENDING,
        no_data=False,
    ),
    ReportMetadata(
        source=Source.META_RIFT_SALES,
        studio_id=STUDIO_ID,
        report_id=438,  # type: ignore
        upload_date=datetime.fromisoformat("2021-01-28 00:00:01.00000Z"),
        blob_name="meta_rift_new_headers.zip",  # type: ignore
        original_name="meta_rift_new_headers.zip",
        date_from=datetime.fromisoformat("2023-08-03"),
        date_to=datetime.fromisoformat("2023-08-04"),
        state=ReportState.PENDING,
        no_data=False,
    ),
]
microsoft_sales_test_metadata = [
    ReportMetadata(
        source=Source.MICROSOFT_SALES,
        studio_id=STUDIO_ID,
        report_id=17,  # type: ignore
        upload_date=datetime.fromisoformat("2021-11-29 00:00:01.00000Z"),
        blob_name="Microsoft_new_manifest_test.zip",  # type: ignore
        original_name="Microsoft_new_manifest_test.zip",
        date_from=datetime.fromisoformat("2022-05-25"),
        date_to=datetime.fromisoformat("2022-05-28"),
        state=ReportState.PENDING,
        no_data=False,
    ),
    ReportMetadata(
        source=Source.MICROSOFT_SALES,
        studio_id=STUDIO_ID,
        report_id=17,  # type: ignore
        upload_date=datetime.fromisoformat("2021-11-29 00:00:01.00000Z"),
        blob_name="MICROSOFT_STORE-2021-05-03_2021-11-01.zip",  # type: ignore
        original_name="MICROSOFT_STORE-2021-05-03_2021-11-01.zip",
        date_from=datetime.fromisoformat("2021-05-03"),
        date_to=datetime.fromisoformat("2021-11-01"),
        state=ReportState.PENDING,
        no_data=False,
    ),
    ReportMetadata(
        source=Source.MICROSOFT_SALES,
        studio_id=STUDIO_ID,
        report_id=17,  # type: ignore
        upload_date=datetime.fromisoformat("2021-07-05 00:00:01.00000Z"),
        blob_name="ms_json.json",  # type: ignore
        original_name="ms_json.json",
        date_from=datetime.fromisoformat("2021-07-01"),
        date_to=datetime.fromisoformat("2021-07-04"),
        state=ReportState.PENDING,
        no_data=False,
    ),
    ReportMetadata(
        source=Source.MICROSOFT_SALES,
        studio_id=STUDIO_ID,
        report_id=17,  # type: ignore
        upload_date=datetime.fromisoformat("2023-10-18 00:00:01.00000Z"),
        blob_name="microsoft_csv.zip",  # type: ignore
        original_name="microsoft_csv.zip",
        date_from=datetime.fromisoformat("2023-07-26"),
        date_to=datetime.fromisoformat("2023-10-18"),
        state=ReportState.PENDING,
        no_data=False,
    ),
    ReportMetadata(
        source=Source.MICROSOFT_SALES,
        studio_id=STUDIO_ID,
        report_id=17,  # type: ignore
        upload_date=datetime.fromisoformat("2023-10-18 00:00:01.00000Z"),
        blob_name="microsoft_csv_no_tax.zip",  # type: ignore
        original_name="microsoft_csv_no_tax.zip",
        date_from=datetime.fromisoformat("2023-07-26"),
        date_to=datetime.fromisoformat("2023-10-18"),
        state=ReportState.PENDING,
        no_data=False,
    ),
    ReportMetadata(
        source=Source.MICROSOFT_SALES,
        studio_id=STUDIO_ID,
        report_id=17,  # type: ignore
        upload_date=datetime.fromisoformat("2021-11-29 00:00:01.00000Z"),
        blob_name="microsoft_json_no_tax.zip",  # type: ignore
        original_name="microsoft_json_no_tax.zip",
        date_from=datetime.fromisoformat("2022-05-25"),
        date_to=datetime.fromisoformat("2022-05-28"),
        state=ReportState.PENDING,
        no_data=False,
    ),
    ReportMetadata(
        source=Source.MICROSOFT_SALES,
        studio_id=STUDIO_ID,
        report_id=17,  # type: ignore
        upload_date=datetime.fromisoformat("2021-11-29 00:00:01.00000Z"),
        blob_name="MICROSOFT_STORE_missing_in_app_name.zip",  # type: ignore
        original_name="MICROSOFT_STORE_missing_in_app_name.zip",
        date_from=datetime.fromisoformat("2021-05-03"),
        date_to=datetime.fromisoformat("2021-11-01"),
        state=ReportState.PENDING,
        no_data=False,
    ),
    ReportMetadata(
        source=Source.MICROSOFT_SALES,
        studio_id=STUDIO_ID,
        report_id=17,  # type: ignore
        upload_date=datetime.fromisoformat("2021-11-29 00:00:01.00000Z"),
        blob_name="Microsoft_inapp_sku.zip",  # type: ignore
        original_name="Microsoft_inapp_sku.zip",
        date_from=datetime.fromisoformat("2021-05-03"),
        date_to=datetime.fromisoformat("2021-11-01"),
        state=ReportState.PENDING,
        no_data=False,
    ),
    ReportMetadata(
        source=Source.MICROSOFT_SALES,
        studio_id=STUDIO_ID,
        report_id=17,  # type: ignore
        upload_date=datetime.fromisoformat("2021-11-29 00:00:01.00000Z"),
        blob_name="Microsoft_applicationName_sku.zip",  # type: ignore
        original_name="Microsoft_applicationName_sku.zip",
        date_from=datetime.fromisoformat("2021-05-03"),
        date_to=datetime.fromisoformat("2021-11-01"),
        state=ReportState.PENDING,
        no_data=False,
    ),
    ReportMetadata(
        source=Source.MICROSOFT_SALES,
        studio_id=STUDIO_ID,
        report_id=433,  # type: ignore
        upload_date=datetime.fromisoformat("2021-11-29 00:00:01.00000Z"),
        blob_name="microsoft_preorder_excluded.zip",  # type: ignore
        original_name="microsoft_preorder_excluded.zip",
        date_from=datetime.fromisoformat("2023-01-18"),
        date_to=datetime.fromisoformat("2023-01-21"),
        state=ReportState.PENDING,
        no_data=False,
    ),
    ReportMetadata(
        source=Source.MICROSOFT_SALES,
        studio_id=STUDIO_ID,
        report_id=434,  # type: ignore
        upload_date=datetime.fromisoformat("2024-10-20 00:00:01.12345Z"),
        blob_name="microsoft_sales-in-python.zip",  # type: ignore
        original_name="microsoft_sales-in-python.zip",
        date_from=datetime.fromisoformat("2024-07-23"),
        date_to=datetime.fromisoformat("2024-10-20"),
        state=ReportState.PENDING,
        no_data=False,
    ),
]
nintendo_discounts_test_metadata = [
    ReportMetadata(
        source=Source.NINTENDO_DISCOUNTS,
        studio_id=STUDIO_ID,
        report_id=3333,  # type: ignore
        upload_date=datetime.fromisoformat("2023-10-15 00:00:01.00000Z"),
        blob_name="nintendo_discounts_v.zip",  # type: ignore
        original_name="nintendo_discounts_v.zip",
        date_from=datetime.fromisoformat("2010-01-01"),
        date_to=datetime.fromisoformat("2023-10-15"),
        state=ReportState.PENDING,
        no_data=False,
    ),
]
nintendo_sales_test_metadata = [
    ReportMetadata(
        source=Source.NINTENDO_SALES,
        studio_id=STUDIO_ID,
        report_id=6,  # type: ignore
        upload_date=datetime.fromisoformat("2021-01-27 00:00:01.00000Z"),
        blob_name="NINTENDO-2018-08-08_2019-07-25.csv",  # type: ignore
        original_name="NINTENDO-2018-08-08_2019-07-25.csv",
        date_from=datetime.fromisoformat("2018-08-08"),
        date_to=datetime.fromisoformat("2019-07-25"),
        state=ReportState.PENDING,
        no_data=False,
    ),
    ReportMetadata(
        source=Source.NINTENDO_SALES,
        studio_id=STUDIO_ID,
        report_id=23,  # type: ignore
        upload_date=datetime.fromisoformat("2022-11-21 00:00:01.00000Z"),
        blob_name="nintendo_sales-2022-03-04_2022-04-04.zip",  # type: ignore
        original_name="nintendo_sales-2022-03-04_2022-04-04.zip",
        date_from=datetime.fromisoformat("2022-03-04"),
        date_to=datetime.fromisoformat("2022-04-04"),
        state=ReportState.PENDING,
        no_data=False,
    ),
    ReportMetadata(
        source=Source.NINTENDO_SALES,
        studio_id=STUDIO_ID,
        report_id=6,  # type: ignore
        upload_date=datetime.fromisoformat("2021-01-27 00:00:01.00000Z"),
        blob_name="NINTENDO-different_title_name.csv",  # type: ignore
        original_name="NINTENDO-different_title_name.csv",
        date_from=datetime.fromisoformat("2018-08-08"),
        date_to=datetime.fromisoformat("2019-07-25"),
        state=ReportState.PENDING,
        no_data=False,
    ),
    ReportMetadata(
        source=Source.NINTENDO_SALES,
        studio_id=STUDIO_ID,
        report_id=23,  # type: ignore
        upload_date=datetime.fromisoformat("2021-12-21 00:00:01.00000Z"),
        blob_name="nintendo_valid.zip",  # type: ignore
        original_name="nintendo_valid.zip",
        date_from=datetime.fromisoformat("2021-11-16"),
        date_to=datetime.fromisoformat("2021-12-17"),
        state=ReportState.PENDING,
        no_data=False,
    ),
    ReportMetadata(
        source=Source.NINTENDO_SALES,
        studio_id=STUDIO_ID,
        report_id=23,  # type: ignore
        upload_date=datetime.fromisoformat("2023-10-21 00:00:01.00000Z"),
        blob_name="nintendo_ymd_date_v.zip",  # type: ignore
        original_name="nintendo_ymd_date_v.zip",
        date_from=datetime.fromisoformat("2023-01-21"),
        date_to=datetime.fromisoformat("2023-02-21"),
        state=ReportState.PENDING,
        no_data=False,
    ),
    ReportMetadata(
        source=Source.NINTENDO_SALES,
        studio_id=STUDIO_ID,
        report_id=34,  # type: ignore
        upload_date=datetime.fromisoformat("2021-12-21 00:00:01.00000Z"),
        blob_name="nintendo_valid_two_files.zip",  # type: ignore
        original_name="nintendo_valid_two_files.zip",
        date_from=datetime.fromisoformat("2021-12-15"),
        date_to=datetime.fromisoformat("2021-12-20"),
        state=ReportState.PENDING,
        no_data=False,
    ),
    ReportMetadata(
        source=Source.NINTENDO_SALES,
        studio_id=STUDIO_ID,
        report_id=21,  # type: ignore
        upload_date=datetime.fromisoformat("2021-02-25 00:00:01.00000Z"),
        blob_name="nintendo_empty_currency.csv",  # type: ignore
        original_name="nintendo_empty_currency.csv",
        date_from=datetime.fromisoformat("2018-08-08"),
        date_to=datetime.fromisoformat("2019-07-25"),
        state=ReportState.PENDING,
        no_data=False,
    ),
    ReportMetadata(
        source=Source.NINTENDO_SALES,
        studio_id=STUDIO_ID,
        report_id=71,  # type: ignore
        upload_date=datetime.fromisoformat("2022-02-22 00:00:01.00000Z"),
        blob_name="nintendo_wiiu_valid.csv",  # type: ignore
        original_name="nintendo_wiiu_valid.csv",
        date_from=datetime.fromisoformat("2009-11-30"),
        date_to=datetime.fromisoformat("2021-12-27"),
        state=ReportState.PENDING,
        no_data=False,
    ),
    ReportMetadata(
        source=Source.NINTENDO_SALES,
        studio_id=STUDIO_ID,
        report_id=427,  # type: ignore
        upload_date=datetime.fromisoformat("2022-05-20 00:00:01.00000Z"),
        blob_name="nintendo_sales-2022-05-01_2022-05-20.zip",  # type: ignore
        original_name="nintendo_sales-2022-05-01_2022-05-20.zip",
        date_from=datetime.fromisoformat("2022-05-01"),
        date_to=datetime.fromisoformat("2022-05-20"),
        state=ReportState.PENDING,
        no_data=False,
    ),
    ReportMetadata(
        source=Source.NINTENDO_SALES,
        studio_id=STUDIO_ID,
        report_id=428,  # type: ignore
        upload_date=datetime.fromisoformat("2022-05-22 00:00:01.00000Z"),
        blob_name="nintendo_sales-2022-05-20_2022-05-23.zip",  # type: ignore
        original_name="nintendo_sales-2022-05-20_2022-05-23.zip",
        date_from=datetime.fromisoformat("2022-05-20"),
        date_to=datetime.fromisoformat("2022-05-23"),
        state=ReportState.PENDING,
        no_data=False,
    ),
    ReportMetadata(
        source=Source.NINTENDO_SALES,
        studio_id=STUDIO_ID,
        report_id=1,  # type: ignore
        upload_date=datetime.fromisoformat("2021-01-26 00:00:01.00000Z"),
        blob_name="nintendo_sales-2018-12-28_2019-01-28.zip",  # type: ignore
        original_name="nintendo_sales-2018-12-28_2019-01-28.zip",
        date_from=datetime.fromisoformat("2018-12-28"),
        date_to=datetime.fromisoformat("2019-01-28"),
        state=ReportState.PENDING,
        no_data=False,
    ),
]
nintendo_wishlist_actions_test_metadata = [
    ReportMetadata(
        source=Source.NINTENDO_WISHLIST_ACTIONS,
        studio_id=STUDIO_ID,
        report_id=15,  # type: ignore
        upload_date=datetime.fromisoformat("2023-02-23 00:00:01.00000Z"),
        blob_name="nintendo_wishlist_actions_valid.zip",  # type: ignore
        original_name="nintendo_wishlist_actions_valid.zip",
        date_from=datetime.fromisoformat("2023-01-14"),
        date_to=datetime.fromisoformat("2023-02-23"),
        state=ReportState.PENDING,
        no_data=False,
    ),
    ReportMetadata(
        source=Source.NINTENDO_WISHLIST_ACTIONS,
        studio_id=STUDIO_ID,
        report_id=15,  # type: ignore
        upload_date=datetime.fromisoformat("2023-02-23 00:00:01.00000Z"),
        blob_name="nintendo_wishlist_actions_zero.zip",  # type: ignore
        original_name="nintendo_wishlist_actions_zero.zip",
        date_from=datetime.fromisoformat("2023-01-14"),
        date_to=datetime.fromisoformat("2023-02-23"),
        state=ReportState.PENDING,
        no_data=False,
    ),
]
nintendo_cumulative_wishlist_sales_test_metadata = [
    ReportMetadata(
        source=Source.NINTENDO_CUMULATIVE_WISHLIST_SALES,
        studio_id=STUDIO_ID,
        report_id=16,  # type: ignore
        upload_date=datetime.fromisoformat("2023-02-23 00:00:01.00000Z"),
        blob_name="nintendo_cumulative_wishlist_sales_valid.zip",  # type: ignore
        original_name="nintendo_cumulative_wishlist_sales_valid.zip",
        date_from=datetime.fromisoformat("2023-01-14"),
        date_to=datetime.fromisoformat("2023-02-23"),
        state=ReportState.PENDING,
        no_data=False,
    ),
    ReportMetadata(
        source=Source.NINTENDO_CUMULATIVE_WISHLIST_SALES,
        studio_id=STUDIO_ID,
        report_id=15,  # type: ignore
        upload_date=datetime.fromisoformat("2023-02-23 00:00:01.00000Z"),
        blob_name="nintendo_cumulative_wishlist_sales_zero.zip",  # type: ignore
        original_name="nintendo_cumulative_wishlist_sales_zero.zip",
        date_from=datetime.fromisoformat("2023-01-14"),
        date_to=datetime.fromisoformat("2023-02-23"),
        state=ReportState.PENDING,
        no_data=False,
    ),
]
playstation_sales_test_metadata = [
    ReportMetadata(
        source=Source.PLAYSTATION_SALES,
        studio_id=STUDIO_ID,
        report_id=9,  # type: ignore
        upload_date=datetime.fromisoformat("2021-01-27 00:00:01.00000Z"),
        blob_name="ps_analytics_emojis.csv",  # type: ignore
        original_name="ps_analytics_emojis.csv",
        date_from=datetime.fromisoformat("2021-04-08"),
        date_to=datetime.fromisoformat("2021-04-13"),
        state=ReportState.PENDING,
        no_data=False,
    ),
    ReportMetadata(
        source=Source.PLAYSTATION_SALES,
        studio_id=STUDIO_ID,
        report_id=9,  # type: ignore
        upload_date=datetime.fromisoformat("2021-01-27 00:00:01.00000Z"),
        blob_name="ps_analytics.csv",  # type: ignore
        original_name="ps_analytics.csv",
        date_from=datetime.fromisoformat("2021-04-08"),
        date_to=datetime.fromisoformat("2021-04-13"),
        state=ReportState.PENDING,
        no_data=False,
    ),
    ReportMetadata(
        source=Source.PLAYSTATION_SALES,
        studio_id=STUDIO_ID,
        report_id=9,  # type: ignore
        upload_date=datetime.fromisoformat("2021-01-27 00:00:01.00000Z"),
        blob_name="ps_analytics_changed_portal_platform_region.csv",  # type: ignore
        original_name="ps_analytics_changed_portal_platform_region.csv",
        date_from=datetime.fromisoformat("2021-04-08"),
        date_to=datetime.fromisoformat("2021-04-13"),
        state=ReportState.PENDING,
        no_data=False,
    ),
    ReportMetadata(
        source=Source.PLAYSTATION_SALES,
        studio_id=STUDIO_ID,
        report_id=10,  # type: ignore
        upload_date=datetime.fromisoformat("2021-01-28 00:00:01.00000Z"),
        blob_name="ps_analytics2.csv",  # type: ignore
        original_name="ps_analytics2.csv",
        date_from=datetime.fromisoformat("2021-05-10"),
        date_to=datetime.fromisoformat("2021-05-14"),
        state=ReportState.PENDING,
        no_data=False,
    ),
    ReportMetadata(
        source=Source.PLAYSTATION_SALES,
        studio_id=STUDIO_ID,
        report_id=12,  # type: ignore
        upload_date=datetime.fromisoformat("2021-02-27 00:00:01.00000Z"),
        blob_name="ps_analytics_new.csv",  # type: ignore
        original_name="ps_analytics_new.csv",
        date_from=datetime.fromisoformat("2021-04-08"),
        date_to=datetime.fromisoformat("2021-04-13"),
        state=ReportState.PENDING,
        no_data=False,
    ),
    ReportMetadata(
        source=Source.PLAYSTATION_SALES,
        studio_id=STUDIO_ID,
        report_id=422,  # type: ignore
        upload_date=datetime.fromisoformat("2022-04-02 00:00:01.00000Z"),
        blob_name="ps_api_json.zip",  # type: ignore
        original_name="ps_api_json.zip",
        date_from=datetime.fromisoformat("2022-03-01"),
        date_to=datetime.fromisoformat("2022-04-01"),
        state=ReportState.PENDING,
        no_data=False,
    ),
    ReportMetadata(
        source=Source.PLAYSTATION_SALES,
        studio_id=STUDIO_ID,
        report_id=431,  # type: ignore
        upload_date=datetime.fromisoformat("2023-01-24 00:00:01.00000Z"),
        blob_name="ps_api_psone.zip",  # type: ignore
        original_name="ps_api_psone.zip",
        date_from=datetime.fromisoformat("2023-01-08"),
        date_to=datetime.fromisoformat("2023-01-12"),
        state=ReportState.PENDING,
        no_data=False,
    ),
    ReportMetadata(
        source=Source.PLAYSTATION_SALES,
        studio_id=STUDIO_ID,
        report_id=431,  # type: ignore
        upload_date=datetime.fromisoformat("2023-01-24 00:00:01.00000Z"),
        blob_name="ps_api_vr2.zip",  # type: ignore
        original_name="ps_api_vr2.zip",
        date_from=datetime.fromisoformat("2023-01-08"),
        date_to=datetime.fromisoformat("2023-01-12"),
        state=ReportState.PENDING,
        no_data=False,
    ),
    ReportMetadata(
        source=Source.PLAYSTATION_SALES,
        studio_id=STUDIO_ID,
        report_id=99995,  # type: ignore
        upload_date=datetime.fromisoformat("2022-01-22 00:00:01.00000Z"),
        blob_name="ps_analytics_promo.csv",  # type: ignore
        original_name="ps_analytics_promo.csv",
        date_from=datetime.fromisoformat("2022-01-24"),
        date_to=datetime.fromisoformat("2021-11-26"),
        state=ReportState.PENDING,
        no_data=False,
    ),
    ReportMetadata(
        source=Source.PLAYSTATION_SALES,
        studio_id=STUDIO_ID,
        report_id=99990,  # type: ignore
        upload_date=datetime.fromisoformat("2022-01-22 00:00:01.00000Z"),
        blob_name="ps_analytics_na_currency.csv",  # type: ignore
        original_name="ps_analytics_na_currency.csv",
        date_from=datetime.fromisoformat("2014-06-14"),
        date_to=datetime.fromisoformat("2014-06-14"),
        state=ReportState.PENDING,
        no_data=False,
    ),
]
playstation_wishlist_actions_test_metadata = [
    ReportMetadata(
        source=Source.PLAYSTATION_WISHLIST_ACTIONS,
        studio_id=STUDIO_ID,
        report_id=15,  # type: ignore
        upload_date=datetime.fromisoformat("2023-07-22 00:00:01.00000Z"),
        blob_name="PS_WISHLISTS-actions_valid.zip",  # type: ignore
        original_name="PS_WISHLISTS-actions_valid.zip",
        date_from=datetime.fromisoformat("2023-07-21"),
        date_to=datetime.fromisoformat("2023-07-21"),
        state=ReportState.PENDING,
        no_data=False,
    ),
]
steam_discounts_test_metadata = [
    ReportMetadata(
        source=Source.STEAM_DISCOUNTS,
        studio_id=STUDIO_ID,
        report_id=333,  # type: ignore
        upload_date=datetime.fromisoformat("2023-10-15 00:00:01.00000Z"),
        blob_name="steam_discounts_v_old_userinfo.zip",  # type: ignore
        original_name="steam_discounts_v_old_userinfo.zip",
        date_from=datetime.fromisoformat("2010-01-01"),
        date_to=datetime.fromisoformat("2023-10-15"),
        state=ReportState.PENDING,
        no_data=False,
    ),
    ReportMetadata(
        source=Source.STEAM_DISCOUNTS,
        studio_id=STUDIO_ID,
        report_id=333,  # type: ignore
        upload_date=datetime.fromisoformat("2023-10-15 00:00:01.00000Z"),
        blob_name="steam_discounts_v.zip",  # type: ignore
        original_name="steam_discounts_v.zip",
        date_from=datetime.fromisoformat("2010-01-01"),
        date_to=datetime.fromisoformat("2023-10-15"),
        state=ReportState.PENDING,
        no_data=False,
    ),
    ReportMetadata(
        source=Source.STEAM_DISCOUNTS,
        studio_id=STUDIO_ID,
        report_id=334,  # type: ignore
        upload_date=datetime.fromisoformat("2023-10-16 00:00:01.00000Z"),
        blob_name="steam_discounts_valid_missing_optinname_column.zip",  # type: ignore
        original_name="steam_discounts_valid_missing_optinname_column.zip",
        date_from=datetime.fromisoformat("2010-01-01"),
        date_to=datetime.fromisoformat("2023-10-16"),
        state=ReportState.PENDING,
        no_data=False,
    ),
]
steam_impressions_test_metadata = [
    ReportMetadata(
        source=Source.STEAM_IMPRESSIONS,
        studio_id=STUDIO_ID,
        report_id=29,  # type: ignore
        upload_date=datetime.fromisoformat("2019-04-13 00:00:01.00000Z"),
        blob_name="STEAM_IMPRESSIONS_OLD_FORMAT.zip",  # type: ignore
        original_name="STEAM_IMPRESSIONS_OLD_FORMAT.zip",
        date_from=datetime.fromisoformat("2016-01-04"),
        date_to=datetime.fromisoformat("2016-02-24"),
        state=ReportState.PENDING,
        no_data=False,
    ),
    ReportMetadata(
        source=Source.STEAM_IMPRESSIONS,
        studio_id=STUDIO_ID,
        report_id=29,  # type: ignore
        upload_date=datetime.fromisoformat("2019-04-13 00:00:01.00000Z"),
        blob_name="STEAM_IMPRESSIONS-2019-02-21_2019-04-13.zip",  # type: ignore
        original_name="STEAM_IMPRESSIONS-2019-02-21_2019-04-13.zip",
        date_from=datetime.fromisoformat("2019-02-21"),
        date_to=datetime.fromisoformat("2019-04-13"),
        state=ReportState.PENDING,
        no_data=False,
    ),
    ReportMetadata(
        source=Source.STEAM_IMPRESSIONS,
        studio_id=STUDIO_ID,
        report_id=421,  # type: ignore
        upload_date=datetime.fromisoformat("2022-04-11 00:00:01.00000Z"),
        blob_name="STEAM_IMPRESSIONS_missing_not_required_cols.zip",  # type: ignore
        original_name="STEAM_IMPRESSIONS_missing_not_required_cols.zip",
        date_from=datetime.fromisoformat("2018-09-18"),
        date_to=datetime.fromisoformat("2018-11-08"),
        state=ReportState.PENDING,
        no_data=False,
    ),
    ReportMetadata(
        source=Source.STEAM_IMPRESSIONS,
        studio_id=STUDIO_ID,
        report_id=437,  # type: ignore
        upload_date=datetime.fromisoformat("2023-07-01 00:00:01.00000Z"),
        blob_name="STEAM_IMPRESSIONS_with_country.zip",  # type: ignore
        original_name="STEAM_IMPRESSIONS_with_country.zip",
        date_from=datetime.fromisoformat("2023-06-30"),
        date_to=datetime.fromisoformat("2023-07-01"),
        state=ReportState.PENDING,
        no_data=False,
    ),
]
steam_sales_test_metadata = [
    ReportMetadata(
        source=Source.STEAM_SALES,
        studio_id=STUDIO_ID,
        report_id=2,  # type: ignore # type: ignore
        upload_date=datetime.fromisoformat("2021-01-27 11:10:00.277000Z"),
        blob_name="STEAM-2018-08-08_2019-07-25.zip",  # type: ignore # type: ignore
        original_name="STEAM-2018-08-08_2019-07-25.zip",
        date_from=datetime.fromisoformat("2018-08-08"),
        date_to=datetime.fromisoformat("2019-07-25"),
        state=ReportState.PENDING,
        no_data=False,
    ),
    ReportMetadata(
        source=Source.STEAM_SALES,
        studio_id=STUDIO_ID,
        report_id=1,  # type: ignore
        upload_date=datetime.fromisoformat("2021-01-26 00:00:01.00000Z"),
        blob_name="steam-one-year.zip",  # type: ignore
        original_name="steam-one-year.zip",
        date_from=datetime.fromisoformat("2010-01-01"),
        date_to=datetime.fromisoformat("2022-12-18"),
        state=ReportState.PENDING,
        no_data=False,
    ),
    ReportMetadata(
        source=Source.STEAM_SALES,
        studio_id=STUDIO_ID,
        report_id=123456,  # type: ignore
        upload_date=datetime.fromisoformat("2021-01-27 00:00:01.00000Z"),
        blob_name="STEAM-promo.zip",  # type: ignore
        original_name="STEAM-promo.zip",
        date_from=datetime.fromisoformat("2018-12-04"),
        date_to=datetime.fromisoformat("2019-12-15"),
        state=ReportState.PENDING,
        no_data=False,
    ),
    ReportMetadata(
        source=Source.STEAM_SALES,
        studio_id=STUDIO_ID,
        report_id=99999,  # type: ignore
        upload_date=datetime.fromisoformat("2022-01-17 00:00:01.00000Z"),
        blob_name="steam_all.zip",  # type: ignore
        original_name="steam_all.zip",
        date_from=datetime.fromisoformat("2000-01-01"),
        date_to=datetime.fromisoformat("2022-01-17"),
        state=ReportState.PENDING,
        no_data=False,
    ),
    ReportMetadata(
        source=Source.STEAM_SALES,
        studio_id=STUDIO_ID,
        report_id=424,  # type: ignore
        upload_date=datetime.fromisoformat("2022-02-05 00:00:01.00000Z"),
        blob_name="steam_short_time_range.zip",  # type: ignore
        original_name="steam_short_time_range.zip",
        date_from=datetime.fromisoformat("2021-02-05"),
        date_to=datetime.fromisoformat("2021-02-28"),
        state=ReportState.PENDING,
        no_data=False,
    ),
    ReportMetadata(
        source=Source.STEAM_SALES,
        studio_id=STUDIO_ID,
        report_id=425,  # type: ignore
        upload_date=datetime.fromisoformat("2022-02-05 00:00:01.00000Z"),
        blob_name="steam_new_sku.zip",  # type: ignore
        original_name="steam_new_sku.zip",
        date_from=datetime.fromisoformat("2021-02-05"),
        date_to=datetime.fromisoformat("2022-02-03"),
        state=ReportState.PENDING,
        no_data=False,
    ),
    ReportMetadata(
        source=Source.STEAM_SALES,
        studio_id=STUDIO_ID,
        report_id=1,  # type: ignore
        upload_date=datetime.fromisoformat("2021-01-26 00:00:01.00000Z"),
        blob_name="steam_sales_2010-01-01_2023-03-15.zip",  # type: ignore
        original_name="steam_sales_2010-01-01_2023-03-15.zip",
        date_from=datetime.fromisoformat("2010-01-01"),
        date_to=datetime.fromisoformat("2023-03-15"),
        state=ReportState.PENDING,
        no_data=False,
    ),
    ReportMetadata(
        source=Source.STEAM_SALES,
        studio_id=STUDIO_ID,
        report_id=1,  # type: ignore
        upload_date=datetime.fromisoformat("2022-06-30 00:00:01.00000Z"),
        blob_name="steam_sales-2022-06-30_2023-06-16-missingdate.zip",  # type: ignore
        original_name="steam_sales-2022-06-30_2023-06-16-missingdate.zip",
        date_from=datetime.fromisoformat("2022-06-30"),
        date_to=datetime.fromisoformat("2023-06-16"),
        state=ReportState.PENDING,
        no_data=False,
    ),
]
steam_wishlist_actions_test_metadata = [
    ReportMetadata(
        source=Source.STEAM_WISHLIST_ACTIONS,
        studio_id=STUDIO_ID,
        report_id=15,  # type: ignore
        upload_date=datetime.fromisoformat("2021-01-27 00:00:01.00000Z"),
        blob_name="STEAM_WISHLISTS-actions_valid.zip",  # type: ignore
        original_name="STEAM_WISHLISTS-actions_valid.zip",
        date_from=datetime.fromisoformat("2021-07-26"),
        date_to=datetime.fromisoformat("2021-09-07"),
        state=ReportState.PENDING,
        no_data=False,
    ),
    ReportMetadata(
        source=Source.STEAM_WISHLIST_ACTIONS,
        studio_id=STUDIO_ID,
        report_id=15,  # type: ignore
        upload_date=datetime.fromisoformat("2021-01-27 00:00:01.00000Z"),
        blob_name="STEAM_WISHLISTS-actions_valid_one_csv_empty.zip",  # type: ignore
        original_name="STEAM_WISHLISTS-actions_valid_one_csv_empty.zip",
        date_from=datetime.fromisoformat("2021-07-26"),
        date_to=datetime.fromisoformat("2021-09-07"),
        state=ReportState.PENDING,
        no_data=False,
    ),
]
steam_wishlist_cohorts_test_metadata = [
    ReportMetadata(
        source=Source.STEAM_WISHLIST_COHORTS,
        studio_id=STUDIO_ID,
        report_id=15,  # type: ignore
        upload_date=datetime.fromisoformat("2021-01-27 00:00:01.00000Z"),
        blob_name="STEAM_WISHLISTS-cohorts_valid.zip",  # type: ignore
        original_name="STEAM_WISHLISTS-cohorts_valid.zip",
        date_from=datetime.fromisoformat("2021-07-26"),
        date_to=datetime.fromisoformat("2021-09-07"),
        state=ReportState.PENDING,
        no_data=False,
    ),
    ReportMetadata(
        source=Source.STEAM_WISHLIST_COHORTS,
        studio_id=STUDIO_ID,
        report_id=15,  # type: ignore
        upload_date=datetime.fromisoformat("2021-01-27 00:00:01.00000Z"),
        blob_name="STEAM_WISHLISTS-cohorts_valid_one_csv_empty.zip",  # type: ignore
        original_name="STEAM_WISHLISTS-cohorts_valid_one_csv_empty.zip",
        date_from=datetime.fromisoformat("2021-07-26"),
        date_to=datetime.fromisoformat("2021-09-07"),
        state=ReportState.PENDING,
        no_data=False,
    ),
]
steam_wishlist_balance_test_metadata = [
    ReportMetadata(
        source=Source.STEAM_WISHLIST_BALANCE,
        studio_id=STUDIO_ID,
        report_id=16,  # type: ignore
        upload_date=datetime.fromisoformat("2025-05-03 00:00:01.00000Z"),
        blob_name="steam_wishlist_balance_v.zip",  # type: ignore
        original_name="steam_wishlist_balance_v.zip",
        date_from=datetime.fromisoformat("2025-05-01"),
        date_to=datetime.fromisoformat("2025-05-02"),
        state=ReportState.PENDING,
        no_data=False,
    ),
]
steam_daily_active_users_test_metadata = [
    ReportMetadata(
        source=Source.STEAM_DAILY_ACTIVE_USERS,
        studio_id=STUDIO_ID,
        report_id=16,  # type: ignore
        upload_date=datetime.fromisoformat("2025-02-01 00:00:01.00000Z"),
        blob_name="dau/steam_daily_active_users_superhot_2025-01.zip",  # type: ignore
        original_name="dau/steam_daily_active_users_superhot_2025-01.zip",
        date_from=datetime.fromisoformat("2025-01-01"),
        date_to=datetime.fromisoformat("2025-01-31"),
        state=ReportState.PENDING,
        no_data=False,
    ),
]

microsoft_daily_active_users_test_metadata = [
    ReportMetadata(
        source=Source.MICROSOFT_DAILY_ACTIVE_USERS,
        studio_id=STUDIO_ID,
        report_id=17,  # type: ignore
        upload_date=datetime.fromisoformat("2025-07-01 00:00:01.00000Z"),
        blob_name="dau/microsoft_daily_active_users_superhot_2025-06.zip",  # type: ignore
        original_name="dau/microsoft_daily_active_users_superhot_2025-06.zip",
        date_from=datetime.fromisoformat("2025-06-01"),
        date_to=datetime.fromisoformat("2025-06-30"),
        state=ReportState.PENDING,
        no_data=False,
    ),
]

microsoft_monthly_active_users_test_metadata = [
    ReportMetadata(
        source=Source.MICROSOFT_MONTHLY_ACTIVE_USERS,
        studio_id=STUDIO_ID,
        report_id=17,  # type: ignore
        upload_date=datetime.fromisoformat("2025-07-01 00:00:01.00000Z"),
        blob_name="mau/microsoft-monthly-active-users-superhot-2025-06.zip",  # type: ignore
        original_name="mau/microsoft-monthly-active-users-superhot-2025-06.zip",
        date_from=datetime.fromisoformat("2025-06-01"),
        date_to=datetime.fromisoformat("2025-06-30"),
        state=ReportState.PENDING,
        no_data=False,
    ),
]

all_test_metadata = (
    []
    + app_store_sales_test_metadata
    + epic_sales_test_metadata
    + gog_sales_test_metadata
    + google_sales_test_metadata
    + humble_sales_test_metadata
    + meta_quest_sales_test_metadata
    + meta_rift_sales_test_metadata
    + microsoft_sales_test_metadata
    + nintendo_discounts_test_metadata
    + nintendo_sales_test_metadata
    + nintendo_wishlist_actions_test_metadata
    + nintendo_cumulative_wishlist_sales_test_metadata
    + playstation_sales_test_metadata
    + playstation_wishlist_actions_test_metadata
    + steam_discounts_test_metadata
    + steam_impressions_test_metadata
    + steam_sales_test_metadata
    + steam_wishlist_actions_test_metadata
    + steam_wishlist_cohorts_test_metadata
    + steam_wishlist_balance_test_metadata
    + steam_daily_active_users_test_metadata
    + microsoft_daily_active_users_test_metadata
    + microsoft_monthly_active_users_test_metadata
)

invalid_test_metadata = [
    (
        ReportMetadata(
            source=Source.HUMBLE_SALES,
            studio_id=STUDIO_ID,
            report_id=26,  # type: ignore
            upload_date=datetime.fromisoformat("2021-06-17 00:00:00.00000Z"),
            blob_name="HUMBLE_INCORRECT_DATE.csv",  # type: ignore
            original_name="HUMBLE_INCORRECT_DATE.csv",
            date_from=datetime.fromisoformat("2020-05-28"),
            date_to=datetime.fromisoformat("2020-05-31"),
            state=ReportState.PENDING,
            no_data=False,
        ),
        "Error while coercing 'transaction_date' to type datetime64[ns]",
    ),
    (
        ReportMetadata(
            source=Source.HUMBLE_SALES,
            studio_id=STUDIO_ID,
            report_id=27,  # type: ignore
            upload_date=datetime.fromisoformat("2021-06-17 00:00:00.00000Z"),
            blob_name="HUMBLE_INCORRECT_NUMBER.csv",  # type: ignore
            original_name="HUMBLE_INCORRECT_NUMBER.csv",
            date_from=datetime.fromisoformat("2018-10-16"),
            date_to=datetime.fromisoformat("2018-10-16"),
            state=ReportState.PENDING,
            no_data=False,
        ),
        "Unable to convert column count to type int64",
    ),
    (
        ReportMetadata(
            source=Source.HUMBLE_SALES,
            studio_id=STUDIO_ID,
            report_id=28,  # type: ignore
            upload_date=datetime.fromisoformat("2021-06-17 00:00:00.00000Z"),
            blob_name="HUMBLE_MISSING_FIELD.csv",  # type: ignore
            original_name="HUMBLE_MISSING_FIELD.csv",
            date_from=datetime.fromisoformat("2018-10-16"),
            date_to=datetime.fromisoformat("2018-10-16"),
            state=ReportState.PENDING,
            no_data=False,
        ),
        "Non-nullable series 'currency' contains null values",
    ),
    (
        ReportMetadata(
            source=Source.EPIC_SALES,
            studio_id=STUDIO_ID,
            report_id=23,  # type: ignore
            upload_date=datetime.fromisoformat("2021-06-17 00:00:01.00000Z"),
            blob_name="EPIC-missing-productid.csv",  # type: ignore
            original_name="EPIC-missing-productid.csv",
            date_from=datetime.fromisoformat("2020-11-23"),
            date_to=datetime.fromisoformat("2020-11-29"),
            state=ReportState.PENDING,
            no_data=False,
        ),
        "Column 'Product Id' not in dataframe",
    ),
    (
        ReportMetadata(
            source=Source.NINTENDO_SALES,
            studio_id=STUDIO_ID,
            report_id=7,  # type: ignore
            upload_date=datetime.fromisoformat("2021-01-28 00:00:01.00000Z"),
            blob_name="NINTENDO-missing-product-names.csv",  # type: ignore
            original_name="NINTENDO-missing-product-names.csv",
            date_from=datetime.fromisoformat("2019-08-19"),
            date_to=datetime.fromisoformat("2021-05-31"),
            state=ReportState.PENDING,
            no_data=False,
        ),
        "Non-nullable series 'TitleCode' contains null values",
    ),
    (
        ReportMetadata(
            source=Source.GOG_SALES,
            studio_id=STUDIO_ID,
            report_id=26,  # type: ignore
            upload_date=datetime.fromisoformat("2021-12-16 00:00:01.00000Z"),
            blob_name="GOG-2021-05-30_2021-12-16_corrupted.zip",  # type: ignore
            original_name="GOG-2021-05-30_2021-12-16_corrupted.zip",
            date_from=datetime.fromisoformat("2021-05-30"),
            date_to=datetime.fromisoformat("2021-12-16"),
            state=ReportState.PENDING,
            no_data=False,
        ),
        "Mismatch in files listed in manifest. Manifest: GOG-2021-05-30_2021-12-16-0.csv GOG-2021-05-30_2021-12-16-1.csv GOG-2021-05-30_2021-12-16-2.csv, actual: GOG-2021-05-30_2021-12-16-0.csv GOG-2021-05-30_2021-12-16-1.csv",
    ),
    (
        ReportMetadata(
            source=Source.STEAM_IMPRESSIONS,
            studio_id=STUDIO_ID,
            report_id=31,  # type: ignore
            upload_date=datetime.fromisoformat("2014-11-13 00:00:01.00000Z"),
            blob_name="STEAM_IMPRESSIONS-corrupted.zip",  # type: ignore
            original_name="STEAM_IMPRESSIONS-corrupted.zip",
            date_from=datetime.fromisoformat("2014-09-23"),
            date_to=datetime.fromisoformat("2014-11-13"),
            state=ReportState.PENDING,
            no_data=False,
        ),
        "Invalid additional data",
    ),
    (
        ReportMetadata(
            source=Source.STEAM_IMPRESSIONS,
            studio_id=STUDIO_ID,
            report_id=31,  # type: ignore
            upload_date=datetime.fromisoformat("2014-11-13 00:00:01.00000Z"),
            blob_name="STEAM_IMPRESSIONS-missing-product-id.zip",  # type: ignore
            original_name="STEAM_IMPRESSIONS-missing-product-id.zip",
            date_from=datetime.fromisoformat("2014-09-23"),
            date_to=datetime.fromisoformat("2014-11-13"),
            state=ReportState.PENDING,
            no_data=False,
        ),
        "Invalid additional data",
    ),
    (
        ReportMetadata(
            source=Source.STEAM_IMPRESSIONS,
            studio_id=STUDIO_ID,
            report_id=32,  # type: ignore
            upload_date=datetime.fromisoformat("2014-11-13 00:00:01.00000Z"),
            blob_name="STEAM_IMPRESSIONS-missing-product-name.zip",  # type: ignore
            original_name="STEAM_IMPRESSIONS-missing-product-name.zip",
            date_from=datetime.fromisoformat("2014-09-23"),
            date_to=datetime.fromisoformat("2014-11-13"),
            state=ReportState.PENDING,
            no_data=False,
        ),
        "Invalid additional data",
    ),
    (
        ReportMetadata(
            source=Source.STEAM_IMPRESSIONS,
            studio_id=STUDIO_ID,
            report_id=33,  # type: ignore
            upload_date=datetime.fromisoformat("2014-11-13 00:00:01.00000Z"),
            blob_name="STEAM_IMPRESSIONS-missing-date.zip",  # type: ignore
            original_name="STEAM_IMPRESSIONS-missing-date.zip",
            date_from=datetime.fromisoformat("2014-09-23"),
            date_to=datetime.fromisoformat("2014-11-13"),
            state=ReportState.PENDING,
            no_data=False,
        ),
        "Invalid additional data",
    ),
    (
        ReportMetadata(
            source=Source.STEAM_SALES,
            studio_id=STUDIO_ID,
            report_id=4,  # type: ignore
            upload_date=datetime.fromisoformat("2021-01-28 00:00:01.00000Z"),
            blob_name="STEAM-missing-product-names.zip",  # type: ignore
            original_name="STEAM-missing-product-names.zip",
            date_from=datetime.fromisoformat("2018-08-08"),
            date_to=datetime.fromisoformat("2018-08-08"),
            state=ReportState.PENDING,
            no_data=False,
        ),
        "Non-nullable series 'Product Name' contains null values",
    ),
    (
        ReportMetadata(
            source=Source.PLAYSTATION_SALES,
            studio_id=STUDIO_ID,
            report_id=10,  # type: ignore
            upload_date=datetime.fromisoformat("2021-01-28 00:00:01.00000Z"),
            blob_name="ps_analytics_missing_product_name.csv",  # type: ignore
            original_name="ps_analytics_missing_product_name.csv",
            date_from=datetime.fromisoformat("2021-04-12"),
            date_to=datetime.fromisoformat("2021-04-12"),
            state=ReportState.PENDING,
            no_data=False,
        ),
        "Non-nullable series 'Product Name' contains null values",
    ),
    (
        ReportMetadata(
            source=Source.PLAYSTATION_SALES,
            studio_id=STUDIO_ID,
            report_id=13,  # type: ignore
            upload_date=datetime.fromisoformat("2021-01-28 00:00:01.00000Z"),
            blob_name="ps_analytics_missing_product_name_new.csv",  # type: ignore
            original_name="ps_analytics_missing_product_name_new.csv",
            date_from=datetime.fromisoformat("2021-04-12"),
            date_to=datetime.fromisoformat("2021-04-12"),
            state=ReportState.PENDING,
            no_data=False,
        ),
        "Non-nullable series 'Product Name' contains null values",
    ),
    (
        ReportMetadata(
            source=Source.PLAYSTATION_SALES,
            studio_id=STUDIO_ID,
            report_id=420,  # type: ignore
            upload_date=datetime.fromisoformat("2021-01-27 00:00:01.00000Z"),
            blob_name="ps_analytics_bad_region.csv",  # type: ignore
            original_name="ps_analytics_bad_region.csv",
            date_from=datetime.fromisoformat("2021-04-08"),
            date_to=datetime.fromisoformat("2021-04-13"),
            state=ReportState.PENDING,
            no_data=False,
        ),
        "PS report contains not migrated region",
    ),
    (
        ReportMetadata(
            source=Source.STEAM_WISHLIST_COHORTS,
            studio_id=STUDIO_ID,
            report_id=16,  # type: ignore
            upload_date=datetime.fromisoformat("2021-01-28 00:00:01.00000Z"),
            blob_name="STEAM_WISHLISTS-CORRUPTED_cohorts.zip",  # type: ignore
            original_name="STEAM_WISHLISTS-CORRUPTED_cohorts.zip",
            date_from=datetime.fromisoformat("2021-07-26"),
            date_to=datetime.fromisoformat("2021-09-07"),
            state=ReportState.PENDING,
            no_data=False,
        ),
        "No objects to concatenate",
    ),
]

empty_test_metadata = [
    ReportMetadata(
        source=Source.STEAM_SALES,
        studio_id=STUDIO_ID,
        report_id=1,  # type: ignore
        upload_date=datetime.fromisoformat("2021-01-26 00:00:01.00000Z"),
        blob_name="STEAM-2010-01-01_2010-12-18.zip",  # type: ignore
        original_name="STEAM-2010-01-01_2010-12-18.zip",
        date_from=datetime.fromisoformat("2010-01-01"),
        date_to=datetime.fromisoformat("2010-12-18"),
        state=ReportState.PENDING,
        no_data=True,
    ),
    ReportMetadata(
        source=Source.EPIC_SALES,
        studio_id=STUDIO_ID,
        report_id=22,  # type: ignore
        upload_date=datetime.fromisoformat("2021-07-14 00:00:01.00000Z"),
        blob_name="EPIC-2018-12-01_2019-06-20_empty.csv",  # type: ignore
        original_name="EPIC-2018-12-01_2019-06-20_empty.csv",
        date_from=datetime.fromisoformat("2018-12-01"),
        date_to=datetime.fromisoformat("2019-06-20"),
        state=ReportState.PENDING,
        no_data=True,
    ),
    ReportMetadata(
        source=Source.HUMBLE_SALES,
        studio_id=STUDIO_ID,
        report_id=25,  # type: ignore
        upload_date=datetime.fromisoformat("2021-06-17 00:00:00.00000Z"),
        blob_name="humble_empty.csv",  # type: ignore
        original_name="humble_empty.csv",
        date_from=datetime.fromisoformat("2020-05-28"),
        date_to=datetime.fromisoformat("2020-05-31"),
        state=ReportState.PENDING,
        no_data=True,
    ),
    ReportMetadata(
        source=Source.GOG_SALES,
        studio_id=STUDIO_ID,
        report_id=430,  # type: ignore
        upload_date=datetime.fromisoformat("2022-02-02 00:00:01.00000Z"),
        blob_name="gog_json_empty.zip",  # type: ignore
        original_name="gog_json_empty.zip",
        date_from=datetime.fromisoformat("2008-09-01"),
        date_to=datetime.fromisoformat("2009-02-01"),
        state=ReportState.PENDING,
        no_data=True,
    ),
    ReportMetadata(
        source=Source.GOG_SALES,
        studio_id=STUDIO_ID,
        report_id=25,  # type: ignore
        upload_date=datetime.fromisoformat("2021-12-16 00:00:01.00000Z"),
        blob_name="GOG-2014-07-20_2015-07-20_empty.zip",  # type: ignore
        original_name="GOG-2014-07-20_2015-07-20_empty.zip",
        date_from=datetime.fromisoformat("2014-07-20"),
        date_to=datetime.fromisoformat("2015-07-20"),
        state=ReportState.PENDING,
        no_data=True,
    ),
    ReportMetadata(
        source=Source.GOOGLE_SALES,
        studio_id=STUDIO_ID,
        report_id=435,  # type: ignore
        upload_date=datetime.fromisoformat("2023-02-20 00:00:01.00000Z"),
        blob_name="google_sales_v_e.zip",  # type: ignore
        original_name="google_sales_v_e.zip",
        date_from=datetime.fromisoformat("2023-03-01"),
        date_to=datetime.fromisoformat("2023-04-30"),
        state=ReportState.PENDING,
        no_data=False,
    ),
    ReportMetadata(
        source=Source.NINTENDO_SALES,
        studio_id=STUDIO_ID,
        report_id=5,  # type: ignore
        upload_date=datetime.fromisoformat("2021-01-26 00:00:01.00000Z"),
        blob_name="nintendo_empty.csv",  # type: ignore
        original_name="nintendo_empty.csv",
        date_from=datetime.fromisoformat("2010-01-01"),
        date_to=datetime.fromisoformat("2010-12-18"),
        state=ReportState.PENDING,
        no_data=True,
    ),
    ReportMetadata(
        source=Source.NINTENDO_SALES,
        studio_id=STUDIO_ID,
        report_id=22,  # type: ignore
        upload_date=datetime.fromisoformat("2021-12-21 00:00:01.00000Z"),
        blob_name="nintendo_empty.zip",  # type: ignore
        original_name="nintendo_empty.zip",
        date_from=datetime.fromisoformat("2019-06-13"),
        date_to=datetime.fromisoformat("2019-07-14"),
        state=ReportState.PENDING,
        no_data=True,
    ),
    ReportMetadata(
        source=Source.NINTENDO_SALES,
        studio_id=STUDIO_ID,
        report_id=23,  # type: ignore
        upload_date=datetime.fromisoformat("2023-10-21 00:00:01.00000Z"),
        blob_name="nintendo_no_sales_observation_v.zip",  # type: ignore
        original_name="nintendo_no_sales_observation_v.zip",
        date_from=datetime.fromisoformat("2022-07-22"),
        date_to=datetime.fromisoformat("2022-08-22"),
        state=ReportState.PENDING,
        no_data=False,
    ),
    ReportMetadata(
        source=Source.NINTENDO_DISCOUNTS,
        studio_id=STUDIO_ID,
        report_id=3332,  # type: ignore
        upload_date=datetime.fromisoformat("2023-10-14 00:00:01.00000Z"),
        blob_name="nintendo_discounts_empty.zip",  # type: ignore
        original_name="nintendo_discounts_empty.zip",
        date_from=datetime.fromisoformat("2010-01-01"),
        date_to=datetime.fromisoformat("2023-10-14"),
        state=ReportState.PENDING,
        no_data=False,
    ),
    ReportMetadata(
        source=Source.NINTENDO_DISCOUNTS,
        studio_id=STUDIO_ID,
        report_id=3332,  # type: ignore
        upload_date=datetime.fromisoformat("2023-10-17 00:00:01.00000Z"),
        blob_name="nintendo_discounts_empty.zip",  # type: ignore
        original_name="nintendo_discounts_empty.zip",
        date_from=datetime.fromisoformat("2010-01-01"),
        date_to=datetime.fromisoformat("2023-10-17"),
        state=ReportState.PENDING,
        no_data=False,
    ),
    ReportMetadata(
        source=Source.NINTENDO_WISHLIST_ACTIONS,
        studio_id=STUDIO_ID,
        report_id=15,  # type: ignore
        upload_date=datetime.fromisoformat("2023-02-23 00:00:01.00000Z"),
        blob_name="nintendo_wishlist_actions_empty.zip",  # type: ignore
        original_name="nintendo_wishlist_actions_empty.zip",
        date_from=datetime.fromisoformat("2023-01-14"),
        date_to=datetime.fromisoformat("2023-02-23"),
        state=ReportState.PENDING,
        no_data=True,
    ),
    ReportMetadata(
        source=Source.NINTENDO_CUMULATIVE_WISHLIST_SALES,
        studio_id=STUDIO_ID,
        report_id=16,  # type: ignore
        upload_date=datetime.fromisoformat("2023-02-23 00:00:01.00000Z"),
        blob_name="nintendo_cumulative_wishlist_sales_empty.zip",  # type: ignore
        original_name="nintendo_cumulative_wishlist_sales_empty.zip",
        date_from=datetime.fromisoformat("2023-01-14"),
        date_to=datetime.fromisoformat("2023-02-23"),
        state=ReportState.PENDING,
        no_data=True,
    ),
    ReportMetadata(
        source=Source.STEAM_IMPRESSIONS,
        studio_id=STUDIO_ID,
        report_id=30,  # type: ignore
        upload_date=datetime.fromisoformat("2014-11-13 00:00:01.00000Z"),
        blob_name="STEAM_IMPRESSIONS-2014-09-23_2014-11-13.zip",  # type: ignore
        original_name="STEAM_IMPRESSIONS-2014-09-23_2014-11-13.zip",
        date_from=datetime.fromisoformat("2014-09-23"),
        date_to=datetime.fromisoformat("2014-11-13"),
        state=ReportState.PENDING,
        no_data=False,
    ),
    ReportMetadata(
        source=Source.STEAM_IMPRESSIONS,
        studio_id=STUDIO_ID,
        report_id=30,  # type: ignore
        upload_date=datetime.fromisoformat("2014-11-13 00:00:01.00000Z"),
        blob_name="STEAM_IMPRESSIONS-empty-na-productId.zip",  # type: ignore
        original_name="STEAM_IMPRESSIONS-empty-na-productId.zip",
        date_from=datetime.fromisoformat("2014-09-23"),
        date_to=datetime.fromisoformat("2014-11-13"),
        state=ReportState.PENDING,
        no_data=False,
    ),
    ReportMetadata(
        source=Source.GOG_SALES,
        studio_id=STUDIO_ID,
        report_id=430,  # type: ignore
        upload_date=datetime.fromisoformat("2022-02-02 00:00:01.00000Z"),
        blob_name="gog_json_empty.zip",  # type: ignore
        original_name="gog_json_empty.zip",
        date_from=datetime.fromisoformat("2008-09-01"),
        date_to=datetime.fromisoformat("2009-02-01"),
        state=ReportState.PENDING,
        no_data=True,
    ),
    ReportMetadata(
        source=Source.GOG_SALES,
        studio_id=STUDIO_ID,
        report_id=25,  # type: ignore
        upload_date=datetime.fromisoformat("2021-12-16 00:00:01.00000Z"),
        blob_name="GOG-2014-07-20_2015-07-20_empty.zip",  # type: ignore
        original_name="GOG-2014-07-20_2015-07-20_empty.zip",
        date_from=datetime.fromisoformat("2014-07-20"),
        date_to=datetime.fromisoformat("2015-07-20"),
        state=ReportState.PENDING,
        no_data=True,
    ),
    ReportMetadata(
        source=Source.PLAYSTATION_SALES,
        studio_id=STUDIO_ID,
        report_id=8,  # type: ignore
        upload_date=datetime.fromisoformat("2021-01-26 00:00:01.00000Z"),
        blob_name="ps_analytics_empty.csv",  # type: ignore
        original_name="ps_analytics_empty.csv",
        date_from=datetime.fromisoformat("2010-01-01"),
        date_to=datetime.fromisoformat("2010-12-18"),
        state=ReportState.PENDING,
        no_data=True,
    ),
    ReportMetadata(
        source=Source.PLAYSTATION_SALES,
        studio_id=STUDIO_ID,
        report_id=11,  # type: ignore
        upload_date=datetime.fromisoformat("2021-02-26 00:00:01.00000Z"),
        blob_name="ps_analytics_empty_new.csv",  # type: ignore
        original_name="ps_analytics_empty_new.csv",
        date_from=datetime.fromisoformat("2010-01-01"),
        date_to=datetime.fromisoformat("2010-12-18"),
        state=ReportState.PENDING,
        no_data=True,
    ),
    ReportMetadata(
        source=Source.PLAYSTATION_SALES,
        studio_id=STUDIO_ID,
        report_id=423,  # type: ignore
        upload_date=datetime.fromisoformat("2022-04-02 00:00:01.00000Z"),
        blob_name="ps_api_json_no_sales.zip",  # type: ignore
        original_name="ps_api_json_no_sales.zip",
        date_from=datetime.fromisoformat("2022-03-01"),
        date_to=datetime.fromisoformat("2022-04-01"),
        state=ReportState.PENDING,
        no_data=False,
    ),
    ReportMetadata(
        source=Source.STEAM_DISCOUNTS,
        studio_id=STUDIO_ID,
        report_id=332,  # type: ignore
        upload_date=datetime.fromisoformat("2023-10-14 00:00:01.00000Z"),
        blob_name="steam_discounts_empty.zip",  # type: ignore
        original_name="steam_discounts_empty.zip",
        date_from=datetime.fromisoformat("2010-01-01"),
        date_to=datetime.fromisoformat("2023-10-14"),
        state=ReportState.PENDING,
        no_data=False,
    ),
    ReportMetadata(
        source=Source.STEAM_WISHLIST_ACTIONS,
        studio_id=STUDIO_ID,
        report_id=14,  # type: ignore
        upload_date=datetime.fromisoformat("2021-01-28 00:00:01.00000Z"),
        blob_name="STEAM_WISHLISTS-2021-08-26_2021-10-07.zip",  # type: ignore
        original_name="STEAM_WISHLISTS-2021-08-26_2021-10-07.zip",
        date_from=datetime.fromisoformat("2021-08-26"),
        date_to=datetime.fromisoformat("2021-10-07"),
        state=ReportState.PENDING,
        no_data=True,
    ),
    ReportMetadata(
        source=Source.STEAM_WISHLIST_COHORTS,
        studio_id=STUDIO_ID,
        report_id=14,  # type: ignore
        upload_date=datetime.fromisoformat("2021-01-28 00:00:01.00000Z"),
        blob_name="STEAM_WISHLISTS-2021-08-26_2021-10-07.zip",  # type: ignore
        original_name="STEAM_WISHLISTS-2021-08-26_2021-10-07.zip",
        date_from=datetime.fromisoformat("2021-08-26"),
        date_to=datetime.fromisoformat("2021-10-07"),
        state=ReportState.PENDING,
        no_data=True,
    ),
    ReportMetadata(
        source=Source.STEAM_WISHLIST_ACTIONS,
        studio_id=STUDIO_ID,
        report_id=41,  # type: ignore
        upload_date=datetime.fromisoformat("2021-01-28 00:00:01.00000Z"),
        blob_name="STEAM_WISHLISTS-empty-no-additional-data.zip",  # type: ignore
        original_name="STEAM_WISHLISTS-empty-no-additional-data.zip",
        date_from=datetime.fromisoformat("2021-08-26"),
        date_to=datetime.fromisoformat("2021-10-07"),
        state=ReportState.PENDING,
        no_data=False,
    ),
    ReportMetadata(
        source=Source.STEAM_WISHLIST_COHORTS,
        studio_id=STUDIO_ID,
        report_id=40,  # type: ignore
        upload_date=datetime.fromisoformat("2021-01-28 00:00:01.00000Z"),
        blob_name="STEAM_WISHLISTS-empty-no-additional-data.zip",  # type: ignore
        original_name="STEAM_WISHLISTS-empty-no-additional-data.zip",
        date_from=datetime.fromisoformat("2021-08-26"),
        date_to=datetime.fromisoformat("2021-10-07"),
        state=ReportState.PENDING,
        no_data=False,
    ),
    ReportMetadata(
        source=Source.STEAM_WISHLIST_ACTIONS,
        studio_id=STUDIO_ID,
        report_id=155,  # type: ignore
        upload_date=datetime.fromisoformat("2021-01-27 00:00:01.00000Z"),
        blob_name="steam_wishlist_manifest_only.zip",  # type: ignore
        original_name="steam_wishlist_manifest_only.zip",
        date_from=datetime.fromisoformat("2022-07-26"),
        date_to=datetime.fromisoformat("2022-09-07"),
        state=ReportState.PENDING,
        no_data=False,
    ),
    ReportMetadata(
        source=Source.META_QUEST_SALES,
        studio_id=STUDIO_ID,
        report_id=22,  # type: ignore
        upload_date=datetime.fromisoformat("2021-01-28 00:00:01.00000Z"),
        blob_name="META_QUEST-2020-07-29_2021-07-29_empty.zip",  # type: ignore
        original_name="META_QUEST-2020-07-29_2021-07-29_empty.zip",
        date_from=datetime.fromisoformat("2021-07-26"),
        date_to=datetime.fromisoformat("2021-09-07"),
        state=ReportState.PENDING,
        no_data=True,
    ),
]
